package golightly

import (
	"crypto/md5"
	"encoding/csv"
	"encoding/hex"
	"fmt"
	"gopkg.in/yaml.v3"
	"log"
	"math/rand"
	"os"
	"strconv"
	"testing"
	"time"
)

func TestLightgbm(t *testing.T) {
	InitializeLightgbm("./lib_lightgbm.so")
	var cErr int

	trained := CreateSimpleRegressionModel("winequality_red_train.tsv", "winequality_red_valid.tsv")

	var numModels int32
	cErr = BoosterNumberOfTotalModel(trained, &numModels)
	fmt.Println("saving model to trained_model.txt")
	cErr = BoosterSaveModel(trained, 0, numModels, 1, "./trained_model.txt")

	// get number of features in model
	var numFeatures int32
	cErr = BoosterGetNumFeature(trained, &numFeatures)
	fmt.Println("number of features in model:", numFeatures)

	// get importance scores
	importance := make([]float64, numFeatures)
	cErr = BoosterFeatureImportance(trained, numModels, Importance_gain, importance)
	fmt.Printf("%+v\n", importance)

	// output model to string
	var outLen64 int64
	modelString := make([]byte, 200_000)
	cErr = BoosterSaveModelToString(trained, 0, numModels, 1, 200_000, &outLen64, modelString)
	fmt.Println("length of model in bytes:", outLen64)
	fmt.Println("----------------Start of model example ----------------------")
	fmt.Println(string(modelString[0:200]))
	fmt.Println("----------------End of model example ----------------------")

	// load model file
	modelFile := "./trained_model.txt"
	iter := int32(0)
	var booster Booster
	cErr = BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}

	// configure fast prediction
	data := []float32{7.4, 0.7, 0, 1.9, 0.076, 11, 34, 0.9978, 3.51, 0.56, 9.4}
	dataType := int32(0)
	nCols := int32(len(data))
	startIter := int32(0)
	numIter := int32(64)
	parameter := ""
	var fastConfig FastConfig
	cErr = BoosterPredictForMatSingleRowFastInit(trained, PredictNormal, startIter, numIter, dataType, nCols, parameter, &fastConfig)

	// make prediction
	var out float64
	cErr = BoosterPredictForMatSingleRowFast(fastConfig, data, &outLen64, &out)
	fmt.Println("Predicted quality:", out)

	cErr = BoosterFree(trained)
}

func BenchmarkLightgbm(b *testing.B) {
	InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./trained_model.txt"
	iter := int32(0)
	var booster Booster
	cErr = BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}

	var dataR []float32
	maxValue := float32(10.0) // 设定最大值
	rawNum := 500
	for i := 0; i < rawNum; i++ {
		for j := 0; j < 11; j++ {
			randomNum := rand.Float32() * maxValue // 生成 [0, maxValue) 之间的随机数
			dataR = append(dataR, randomNum)
		}
	}
	startIter := int32(0)
	numIter := int32(-1)
	var outLen64 int64
	nRaw, nCol := int32(rawNum), int32(11)
	outval := make([]float64, nRaw)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		BoosterPredictForMat(booster, dataR, Dtype_float32, nRaw, nCol, Row_major, PredictNormal,
			startIter, numIter, "", &outLen64, outval)
	}
	BoosterFree(booster)
}

func BenchmarkDemo(b *testing.B) {
	InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./demo/ctr_model"
	iter := int32(0)
	var booster Booster
	cErr = BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}
	var numFeatures int32
	cErr = BoosterGetNumFeature(booster, &numFeatures)
	//fmt.Printf("model features num: %d\n", numFeatures)

	configs, err := loadOrderedYAML("./demo/ctr_fealist.yaml")
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	var features []string
	// **按顺序打印**
	for _, feature := range configs {
		//fmt.Printf("Feature: %s\n", feature.Key)
		//fmt.Printf("  Enable: %v\n", feature.Enable)
		//fmt.Printf("  Value: %d\n", feature.Value)
		//fmt.Printf("  Method: %s\n", feature.FeatureProcessing.Method)
		//fmt.Printf("  Params: %v\n", feature.FeatureProcessing.Params)
		//fmt.Printf("  RequiresStatistics: %v\n\n", feature.RequiresStatistics)
		if feature.Enable {
			features = append(features, feature.Key)
		}
	}

	//加载日志数据和统计特征数据
	logData, _ := loadCSV("./demo/shitu.csv")
	logDataM := convertCSV(logData)
	statsData, _ := loadCSV("./demo/feature_stats.csv")

	// 生成特征和标签
	featureMatrix, _, _ := generateFeatures(logDataM, statsData, features)

	nRaw, nCol := int32(len(featureMatrix)/len(features)), int32(len(features))
	// 注意这个要提前分配内存
	outval := make([]float64, nRaw)
	var outLen64 int64
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		BoosterPredictForMat(booster, featureMatrix, Dtype_float32, nRaw, nCol, Row_major, PredictNormal,
			0, -1, "", &outLen64, outval)
	}
	//fmt.Printf("outlen %d\n", outLen64)
	//for i, v := range outval {
	//	fmt.Printf("outval %d, data %f\n", i, v)
	//}

	//保存结果
	//outputFile, _ := os.Create("./demo/predictions_demo.txt")
	//defer outputFile.Close()
	//writer := csv.NewWriter(outputFile)
	//writer.Comma = '\t'
	//writer.Write([]string{"bid_id", "y_true", "y_pred"})
	//for i, bidID := range bidIDs {
	//	writer.Write([]string{bidID, strconv.Itoa(clicks[i]), fmt.Sprintf("%f", outval[i])})
	//}
	//writer.Flush()
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// 加载 CSV 文件
func loadCSV(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '\t' // TSV 文件
	return reader.ReadAll()
}

var fields = []string{
	"bid_id", "impression_time", "dt", "hour", "device_id", "device_id_type",
	"device_brand", "device_model", "device_type", "os", "os_version", "country",
	"network_type", "ip", "tag_id", "delivery_type", "traffic_material_size",
	"app_id", "ad_id", "material_id_type", "title_id", "description_id",
	"delivery_material_size", "image_material_id", "video_material_id",
	"package_name", "install_list", "install_list_user", "user_agent",
	"channel_id", "creative_id", "advertiser_type", "user_id", "timezone",
	"account_id", "project_id", "click_time", "click_unique_id", "cost",
	"conversion_time", "conversion_type", "conversion_price",
}

// 需要改为map
func convertCSV(lines [][]string) []map[string]string {
	ret := make([]map[string]string, 0, len(lines))
	for _, line := range lines {
		m := make(map[string]string)
		for i, t := range line {
			m[fields[i]] = t
		}
		ret = append(ret, m)
	}
	return ret
}

// 生成 CTR 特征
type Feature struct {
	Feasign    string
	ShowStats  float64
	ClickStats float64
}

type FeatureProcessing struct {
	Method string            `yaml:"method"`
	Params map[string]string `yaml:"params"`
}

type FeatureConfig struct {
	Key                string            // 额外存储键名
	Enable             bool              `yaml:"enable"`
	Value              int               `yaml:"value"`
	FeatureProcessing  FeatureProcessing `yaml:"feature_processing"`
	RequiresStatistics bool              `yaml:"requires_statistics"`
}

// 自定义解析函数，保证顺序
// **使用 yaml.Node 保持顺序**
func loadOrderedYAML(filename string) ([]FeatureConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var rootNode yaml.Node
	err = yaml.Unmarshal(data, &rootNode)
	if err != nil {
		return nil, err
	}

	// **如果是 DocumentNode，进入它的子节点**
	if rootNode.Kind == yaml.DocumentNode && len(rootNode.Content) > 0 {
		rootNode = *rootNode.Content[0] // 进入根 MappingNode
	}

	// **确保是 MappingNode**
	if rootNode.Kind != yaml.MappingNode {
		return nil, fmt.Errorf("expected MappingNode, got %v", rootNode.Kind)
	}

	// **遍历 MappingNode 保持顺序**
	var orderedConfigs []FeatureConfig
	for i := 0; i < len(rootNode.Content); i += 2 {
		keyNode := rootNode.Content[i]     // YAML 的 key
		valueNode := rootNode.Content[i+1] // YAML 的 value

		var feature FeatureConfig
		err := valueNode.Decode(&feature) // 解析 value 部分
		if err != nil {
			return nil, err
		}
		feature.Key = keyNode.Value // 记录 key 名称
		orderedConfigs = append(orderedConfigs, feature)
	}

	return orderedConfigs, nil
}

func generateFeatures(logData []map[string]string, statsData [][]string, features []string) ([]float32, []int, []string) {
	featureMap := make(map[string]Feature)
	//['feasign', 'show_stats', 'click_stats', 'cv_stats']
	for _, row := range statsData {
		show, _ := strconv.ParseFloat(row[1], 64)
		click, _ := strconv.ParseFloat(row[2], 64)
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: show, ClickStats: click}
	}

	var featureMatrix []float32
	var clicks []int
	var bidIDs []string
	priorCTR := 0.01 // 先验CTR
	for _, row := range statsData {
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1], 64)
			globalClick, _ := strconv.ParseFloat(row[2], 64)
			priorCTR = globalClick / globalShow
		}
	}

	alpha, beta := 0.1, 500.0

	for _, row := range logData {
		layout := "2006-01-02 15:04:05"
		impressionTime, _ := time.Parse(layout, row["impression_time"])
		dayOfWeek := impressionTime.Weekday().String()[:3]
		eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
		row["day_of_week"] = dayOfWeek
		row["event_hour"] = eventHour
		var featureRow []float32
		for _, feature := range features {
			feasign := md5Hash(feature + "::" + row[feature])
			stat, exists := featureMap[feasign]
			if !exists || stat.ShowStats < 10 {
				ctr := (stat.ClickStats + alpha*beta*priorCTR) / (stat.ShowStats + alpha*beta)
				featureRow = append(featureRow, float32(ctr))
			} else {
				featureRow = append(featureRow, float32(stat.ClickStats/stat.ShowStats))
			}
		}

		click := 0
		if row["click_time"] != "" { // click_time 是否为空
			click = 1
		}
		if len(clicks) == 500 {
			break
		}
		featureMatrix = append(featureMatrix, featureRow...)
		clicks = append(clicks, click)
		bidIDs = append(bidIDs, row["bid_id"])
	}

	return featureMatrix, clicks, bidIDs
}
