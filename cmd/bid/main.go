package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"log"
	"math"
	"os"
	"strconv"
	"time"
)

var features = []string{
	"channel_id",
	"country",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"app_id",
	"ad_id",
	"material_id_type",
	"title_id",
	"description_id",
	"bid_price",
	"delivery_type",
	"ad_slot_type",
	"tag_id",
	"day_of_week",
	"event_hour",
	"timezone",
	"account_id",
	"project_id",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"traffic_material_size",
	"device_model",
	"delivery_package",
}

var catFeatures = []string{
	"channel_id",
	"country",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"app_id",
	"ad_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"ad_slot_type",
	"tag_id",
	"day_of_week",
	"event_hour",
	"timezone",
	"account_id",
	"project_id",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"traffic_material_size",
	"device_model",
	"delivery_package",
}

var categoryMap catTable

var featureMap = make(map[string]Feature)

var priorCTR float32
var alpha float32
var beta float32

var booster gl.Booster
var fastConfig gl.FastConfig

func main() {
	pctr := 0.0012
	pcvr := 0.020
	cpa := 1000.0
	V := pctr * pcvr * cpa * 1000
	V = 20
	fmt.Printf("V: %.4f\n", V)

	//epsilon := float32(1.0)
	//N := 5

	gl.InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./demo/bid/local_bidding_model.txt"
	iter := int32(0)
	cErr = gl.BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}
	var numFeatures int32
	cErr = gl.BoosterGetNumFeature(booster, &numFeatures)
	fmt.Printf("model features num: %d\n", numFeatures)

	Categories_str, err := ReadPandasCategorical(modelFile)
	fmt.Println(Categories_str)
	if err != nil {
		log.Fatal(err)
	}

	category := Categories{}
	json.Unmarshal([]byte(Categories_str), &category)
	categoryMap = make(catTable)
	for i, v := range catFeatures {
		if v == "bid_price" {
			continue
		}
		categoryMap[v] = make(map[string]float64)
		for j, val := range category[i] {
			switch val := val.(type) {
			case string:
				categoryMap[v][val] = float64(j)
			case int, int32, int64, float32, float64:
				categoryMap[v][fmt.Sprintf("%v", val)] = float64(j)
			default:
				// 其他类型处理（如果需要）
			}
		}
	}

	logData, _ := loadCSV("./demo/bid/response_log.csv")
	logDataM := convertCSV(logData)
	statsData, _ := loadCSV("./demo/bid/feature_sign_bidding.txt")

	for _, row := range statsData {
		show, _ := strconv.Atoi(row[1])
		click, _ := strconv.Atoi(row[2])
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	}

	priorCTR = 0.01 // 先验CTR
	for _, row := range statsData {
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1], 64)
			globalClick, _ := strconv.ParseFloat(row[2], 64)
			priorCTR = float32(globalClick) / float32(globalShow)
		}
	}

	alpha, beta = 0.1, 500.0

	cErr = gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, 0, -1, gl.Dtype_float32, numFeatures, "", &fastConfig)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return
	}
	var out float64
	var outLen64 int64
	data := make([]float32, numFeatures)
	data[12] = 10
	cErr = gl.BoosterPredictForMatSingleRowFast(fastConfig, data, &outLen64, &out)
	fmt.Println("Predict: ", out)
	V = 5
	//optimalBid := goldenSectionSearch(float32(V), epsilon, N, data)
	//fmt.Printf("Optimal bid: %.4f\n", optimalBid)

	bidmodel := NewBidding(booster, data, featureMap)
	//for i := 100; i > 0; i-- {
	//	r := bidmodel.predictBiddingWinRate(float32(i))
	//	fmt.Printf(" %d rate: %.4f\n", i, r)
	//}
	//
	//for i := 100; i > 0; i-- {
	//	t := bidmodel.GoldenSectionSearch(float32(i))
	//	fmt.Printf("bid is %d, Optimal bid: %.4f\n", i, t)
	//}

	feas, _, ids := generateFeaturesBid(logDataM, statsData, features)
	var oriprice []float64
	var price []float64
	for _, v := range feas {
		bidmodel.SetFea(v)
		oriprice = append(oriprice, math.Round(float64(v[12])))
		r := bidmodel.GoldenSectionSearch(float32(v[12]))
		price = append(price, math.Round(float64(r)))
	}
	outputFile, _ := os.Create("./demo/bid/predictions_demo.txt")
	defer outputFile.Close()
	writer := csv.NewWriter(outputFile)
	writer.Comma = '\t'
	writer.Write([]string{"bid_id", "y_pred"})
	for i, bidID := range ids {
		writer.Write([]string{bidID, fmt.Sprintf("%f", oriprice[i]), fmt.Sprintf("%f", price[i])})
	}
	writer.Flush()
}

func generateFeaturesBid(logData []map[string]string, statsData [][]string, features []string) ([][]float32, []int, []string) {
	//['feasign', 'show_stats', 'click_stats', 'cv_stats']
	for _, row := range statsData {
		show, _ := strconv.Atoi(row[1])
		click, _ := strconv.Atoi(row[2])
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	}

	var featureMatrix [][]float32
	var clicks []int
	var bidIDs []string
	featureMatrix = make([][]float32, len(logData))
	var (
		cat    = make([]float32, 0, len(features))
		show   = make([]float32, 0, len(features))
		click  = make([]float32, 0, len(features))
		ctrval = make([]float32, 0, len(features))
	)

	for i, row := range logData {

		layout := "2006-01-02 15:04:05"
		impressionTime, _ := time.Parse(layout, row["response_time"])
		loc := time.FixedZone("Custom", timezoneOffsets[row["country"]])
		impressionTime = impressionTime.In(loc)
		dayOfWeek := impressionTime.Weekday().String()[:3]
		eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
		row["day_of_week"] = dayOfWeek
		row["event_hour"] = eventHour
		//var featureRow []float64
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]

		for _, feature := range features {

			if feature == "bid_price" {
				bid, _ := strconv.ParseFloat(row[feature], 64)
				cat = append(cat, float32(bid))
			} else {
				cat = append(cat, float32(categoryMap.GetVal(feature, row[feature])))
			}

			feasign := md5Hash(feature + "::" + row[feature])
			stat, exists := featureMap[feasign]
			if !exists {
				show = append(show, 0)
				click = append(click, 0)
			} else {
				show = append(show, stat.ShowStats)
				click = append(click, stat.ClickStats)
			}

			if !exists || stat.ShowStats < 10 {
				ctr := (stat.ClickStats + float32(alpha*beta*priorCTR)) / (stat.ShowStats + float32(alpha*beta))
				//featureRow = append(featureRow, (ctr))
				ctrval = append(ctrval, (ctr))
			} else {
				//featureRow = append(featureRow, (stat.ClickStats / stat.ShowStats))
				ctrval = append(ctrval, (stat.ClickStats / stat.ShowStats))
			}
		}

		click_1 := 0
		if row["click_time"] != "" { // click_time 是否为空
			click_1 = 1
		}
		featureMatrix[i] = append(featureMatrix[i], cat...)
		featureMatrix[i] = append(featureMatrix[i], show...)
		featureMatrix[i] = append(featureMatrix[i], click...)
		featureMatrix[i] = append(featureMatrix[i], ctrval...)
		//for _, v := range featureMatrix {
		//	fmt.Printf("%f\n", v)
		//}
		clicks = append(clicks, click_1)
		bidIDs = append(bidIDs, row["bid_id"])
		if row["bid_id"] == "9f69610c3faa0b487dbad8398f9d38fd5067" {
			b, _ := json.Marshal(row)
			fmt.Println(string(b))
		}

	}

	return featureMatrix, clicks, bidIDs
}
