package main

import (
	"encoding/json"
	"fmt"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"io/ioutil"
	"math"
	"os"
	"strconv"
)

const (
	BidIndex1 = 12
	BidIndex2 = 12 + 27
	BidIndex3 = 12 + 27 + 27
	BidIndex4 = 12 + 27 + 27 + 27

	BidFeaNum = 108

	GoldenSection        = 1.618033989
	GoldenSectionIterNum = 5
	GoldenSectionEpsilon = 1.0
)

type Bidding struct {
	fea        []float32
	fastConfig gl.FastConfig
	feaSignMap map[string]Feature
}

func NewBidding(booster gl.Booster, fea []float32, feaSignMap map[string]Feature) *Bidding {
	var fc gl.FastConfig
	cErr := gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, 0, -1, gl.Dtype_float32, BidFeaNum, "", &fc)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return nil
	}
	bd := &Bidding{
		fea:        fea,
		fastConfig: fc,
		feaSignMap: feaSignMap,
	}
	return bd
}

func (b *Bidding) predictBiddingWinRate(bid float32) float64 {
	bidI := math.Round(float64(bid))
	show, click, ctr := b.getBidFeas(int(bidI))
	b.fea[BidIndex1] = float32(bidI)
	b.fea[BidIndex2] = show
	b.fea[BidIndex3] = click
	b.fea[BidIndex4] = ctr

	var outLen64 int64
	var out float64
	cErr := gl.BoosterPredictForMatSingleRowFast(fastConfig, b.fea, &outLen64, &out)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return 0
	}
	out = model.Predict(out)

	return out
}

func (b *Bidding) sFunc(V, bid float32) float64 {
	return float64(V-bid) * b.predictBiddingWinRate(bid)
}

func (b *Bidding) GoldenSectionSearch(V float32) float32 {
	// 初始化边界
	bMin, bMax := float32(1.0), V

	// 计算初始内部点
	x1 := bMax - (bMax-bMin)/GoldenSection
	x2 := bMin + (bMax-bMin)/GoldenSection

	// 开始搜索
	for i := 0; i < GoldenSectionIterNum; i++ {
		sX1 := b.sFunc(V, x1)
		sX2 := b.sFunc(V, x2)
		//fmt.Printf("curr iter: %d, x1: %.4f, x2: %.4f, bMin: %.4f, bMax: %.4f, sX1: %.4f, sX2: %.4f\n",
		//	i, x1, x2, bMin, bMax, sX1, sX2)

		if sX1 > sX2 {
			bMax = x2
		} else {
			bMin = x1
		}

		// 如果区间足够小，提前终止
		if bMax-bMin < GoldenSectionEpsilon {
			break
		}

		x1 = bMax - (bMax-bMin)/GoldenSection
		x2 = bMin + (bMax-bMin)/GoldenSection
	}

	return (bMin + bMax) / 2
}

func (b *Bidding) getBidFeas(bid int) (show float32, click float32, ctr float32) {
	feasign := md5Hash("bid_price::" + strconv.Itoa(bid))
	stat, exists := b.feaSignMap[feasign]
	if !exists {
		show = 0
		click = 0
	} else {
		show = stat.ShowStats
		click = stat.ClickStats
	}
	if !exists || stat.ShowStats < 10 {
		ctr = (stat.ClickStats + alpha*beta*priorCTR) / (stat.ShowStats + alpha*beta)
	} else {
		ctr = stat.ClickStats / stat.ShowStats
	}
	return
}

func (b *Bidding) SetFea(fea []float32) {
	b.fea = fea
}

type Model struct {
	X []float64 `json:"X"`
	Y []float64 `json:"y"`
}

// 线性插值预测函数
func (m *Model) Predict(x float64) float64 {
	if x <= m.X[0] {
		return m.Y[0]
	}
	if x >= m.X[len(m.X)-1] {
		return m.Y[len(m.Y)-1]
	}

	for i := 0; i < len(m.X)-1; i++ {
		if x >= m.X[i] && x <= m.X[i+1] {
			x0, x1 := m.X[i], m.X[i+1]
			y0, y1 := m.Y[i], m.Y[i+1]
			t := (x - x0) / (x1 - x0)
			return y0 + t*(y1-y0)
		}
	}
	return 0 // 理论不会到这
}

var model Model

func init() {
	jsonFile, err := os.Open("./demo/bid/cali_bidding_model.json")
	if err != nil {
		panic(err)
	}
	defer jsonFile.Close()

	byteValue, _ := ioutil.ReadAll(jsonFile)
	json.Unmarshal(byteValue, &model)
}
