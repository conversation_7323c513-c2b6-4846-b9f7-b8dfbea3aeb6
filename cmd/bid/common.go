package main

import (
	"crypto/md5"
	"encoding/csv"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"strings"
)

type Categories [][]interface{}

//var Categories_str = `[["104dac5750cf9d87"], [336, 1007, 1097, 1447, 1539, 1880, 1897, 1920, 2123, 2128, 2167, 2366, 2387, 2624, 2715, 2806, 2820, 2997, 3055, 3063, 3065, 3066, 3131, 3386, 3404, 3433, 3908, 3910, 4017, 4129, 4130, 4156, 4356, 4409, 4431, 4432, 4433, 4434, 4464], [223, 2371, 2372, 2373, 2374, 2662, 2663, 3764, 3930, 3931, 4985, 5056, 5057, 5058, 5113, 5115, 5547, 5555, 5634, 5635, 5636, 6761, 7021, 7276, 7277, 7306, 7307, 7862, 8052, 8053, 8102, 8104, 8105, 8222, 8223, 8224, 8835, 8873, 8961, 10097, 10098, 10109, 10110, 10387, 10608, 10611, 10659, 10840, 11189, 11328, 11398, 11399, 11400, 11401, 11463], ["BRA", "DEU", "IDN", "MYS", "PAK", "RUS", "SAU", "VNM"], ["Fri", "Sat"], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], ["oppo"], [0], ["Android"], ["10", "11", "12", "13", "14", "15", "5.1", "5.1.1", "6.0", "6.0.1", "7.1.1", "8.1.0", "9"], [2.0], [2], ["oppo-1004470"], ["com.alibaba.aliexpresshd", "com.amazon.mShop.android.shopping", "com.arcsoft.perfect365", "com.fugo.wow", "com.gojek.gopaymerchant", "com.hwsj.club", "com.king.candycrushsaga", "com.kubi.kucoin", "com.live.soulchill", "com.voicemaker.android", "com.xparty.androidapp", "com.yandex.searchapp", "com.zzkko", "ru.cvetov.app", "ru.ekonika.app", "ru.gibdd_pay.app", "ru.tinkoff.bnpl", "ru.uteka.app", "ru.yandex.music", "ru.yandex.yandexmaps", "sg.bigo.live"], ["GPID"], ["+00:00"], [5.0, 6.0], [39.0, 152.0, 428.0, 558.0, 600.0, 757.0, 794.0, 810.0, 812.0, 890.0, 892.0, 933.0, 938.0, 1059.0, 1067.0, 1195.0, 1207.0, 1232.0, 1269.0, 1281.0, 1320.0, 1393.0, 1397.0, 1399.0, 1400.0, 1416.0, 1805.0, 1807.0, 1865.0, 1921.0, 1922.0, 1935.0, 2046.0, 2061.0, 2073.0, 2074.0, 2075.0, 2076.0, 2083.0], [2.0], [527.0, 778.0, 796.0, 876.0, 888.0, 981.0, 990.0, 1001.0, 1013.0, 1043.0, 1045.0, 1067.0, 1071.0, 1270.0, 1284.0, 1312.0, 1338.0, 1343.0, 1380.0, 1453.0, 1457.0, 1458.0, 1459.0, 1478.0, 1481.0, 1786.0, 1787.0, 1830.0, 1881.0, 1882.0, 1888.0, 1915.0, 1950.0, 1966.0, 1977.0, 1978.0, 1979.0, 1980.0, 1987.0], [617.0, 1076.0, 1077.0, 1078.0, 1079.0, 1132.0, 1133.0, 1333.0, 1365.0, 1366.0, 1586.0, 1600.0, 1613.0, 1614.0, 1615.0, 1649.0, 1651.0, 1727.0, 1728.0, 1729.0, 1731.0, 1732.0, 1774.0, 1780.0, 2180.0, 2199.0, 2239.0, 2295.0, 2296.0, 2307.0, 2308.0, 2373.0, 2465.0, 2466.0, 2471.0, 2472.0, 2473.0, 2493.0, 2496.0, 2847.0, 2848.0, 2849.0, 2850.0, 2905.0, 2957.0, 2958.0, 2964.0, 2993.0, 3042.0, 3059.0, 3072.0, 3073.0, 3074.0, 3075.0, 3082.0], [3.0], ["300", "300x", "300x3", "300x30", "300x300"], ["3", "30", "300", "300x", "300x3", "300x30", "300x300"], [1.0, 2.0, 17.0, 69.0, 89.0, 229.0, 303.0, 710.0, 753.0, 941.0, 989.0, 1190.0, 1228.0, 1245.0, 1247.0, 1341.0, 1343.0, 1383.0, 1388.0, 1483.0, 1547.0, 1558.0, 1699.0, 1727.0, 1767.0, 1834.0, 1849.0, 1909.0, 2060.0, 2065.0, 2067.0, 2068.0, 2084.0, 2354.0, 2452.0, 2462.0], [0.0]]`

type catTable map[string]map[string]float64

func (c *catTable) GetVal(fea, key string) float64 {
	if _, ok := (*c)[fea][key]; ok {
		return (*c)[fea][key]
	}
	return -1
}

const PandasCategorical = "pandas_categorical:"

func ReadPandasCategorical(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	var lines []string
	var line []byte

	// 移动到文件末尾
	_, err = file.Seek(0, io.SeekEnd)
	if err != nil {
		return "", err
	}

	for {
		// 从文件末尾向前读取一个字符
		pos, err := file.Seek(-1, io.SeekCurrent)
		if err != nil {
			break
		}

		// 读取字符
		b := make([]byte, 1)
		_, err = file.Read(b)
		if err != nil {
			break
		}

		// 如果读取到换行符，表示找到了一行
		if b[0] == '\n' {
			lines = append(lines, string(line))
			if strings.HasPrefix(string(line), PandasCategorical) {
				return strings.Replace(string(line), PandasCategorical, "", 1), nil
			}
			line = nil // 重置当前行
		} else {
			line = append([]byte{b[0]}, line...) // 将字符添加到当前行的开头
		}

		// 如果已经到达文件开头，退出循环
		if pos == 0 {
			lines = append(lines, string(line)) // 添加最后一行
			break
		}

		// 继续向前移动文件指针
		_, err = file.Seek(-1, io.SeekCurrent)
		if err != nil {
			break
		}
	}

	return "", fmt.Errorf("can not find datapandas_categorical")
}

type Feature struct {
	Feasign    string
	ShowStats  float32
	ClickStats float32
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// 加载 CSV 文件
func loadCSV(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '\t' // TSV 文件
	return reader.ReadAll()
}

var timezoneOffsets = map[string]int{
	"USA": -18000, // GMT-05:00
	"CAN": -18000, // GMT-05:00
	"MEX": -25200, // GMT-07:00
	"BRA": -14400, // GMT-04:00
	"ARG": -10800, // GMT-03:00
	"GBR": 0,      // GMT+00:00
	"FRA": 3600,   // GMT+01:00
	"DEU": 3600,   // GMT+01:00
	"ITA": 3600,   // GMT+01:00
	"ESP": 3600,   // GMT+01:00
	"RUS": 10800,  // GMT+03:00
	"CHN": 28800,  // GMT+08:00
	"IND": 19800,  // GMT+05:30
	"JPN": 32400,  // GMT+09:00
	"AUS": 34200,  // GMT+09:30
	"ZAF": 7200,   // GMT+02:00
	"NGA": 3600,   // GMT+01:00
	"EGY": 7200,   // GMT+02:00
	"SAU": 10800,  // GMT+03:00
	"UAE": 14400,  // GMT+04:00
	"TUR": 10800,  // GMT+03:00
	"ISR": 7200,   // GMT+02:00
	"IRN": 12600,  // GMT+03:30
	"PAK": 18000,  // GMT+05:00
	"AFG": 16200,  // GMT+04:30
	"THA": 25200,  // GMT+07:00
	"VNM": 25200,  // GMT+07:00
	"KOR": 32400,  // GMT+09:00
	"IDN": 28800,  // GMT+08:00
	"NZL": 43200,  // GMT+12:00
	"PHL": 28800,  // GMT+08:00
	"MYS": 28800,  // GMT+08:00
	"SGP": 28800,  // GMT+08:00
	"BGD": 21600,  // GMT+06:00
	"LKA": 19800,  // GMT+05:30
	"NPL": 20700,  // GMT+05:45
	"BTN": 21600,  // GMT+06:00
	"MMR": 23400,  // GMT+06:30
	"LAO": 25200,  // GMT+07:00
	"KHM": 25200,  // GMT+07:00
	"PRK": 32400,  // GMT+09:00
	"MNG": 28800,  // GMT+08:00
	"KAZ": 18000,  // GMT+05:00
	"UZB": 18000,  // GMT+05:00
	"TJK": 18000,  // GMT+05:00
	"KGZ": 21600,  // GMT+06:00
	"TKM": 18000,  // GMT+05:00
	"AZE": 14400,  // GMT+04:00
	"GEO": 14400,  // GMT+04:00
	"ARM": 14400,  // GMT+04:00
	"IRQ": 10800,  // GMT+03:00
	"SYR": 7200,   // GMT+02:00
	"LBN": 7200,   // GMT+02:00
	"JOR": 7200,   // GMT+02:00
	"YEM": 10800,  // GMT+03:00
	"OMN": 14400,  // GMT+04:00
	"QAT": 10800,  // GMT+03:00
	"BHR": 10800,  // GMT+03:00
	"KWT": 10800,  // GMT+03:00
	"ARE": 14400,  // GMT+04:00
	"PSE": 7200,   // GMT+02:00
	"CYP": 7200,   // GMT+02:00
	"MLT": 3600,   // GMT+01:00
	"GRC": 7200,   // GMT+02:00
	"BGR": 7200,   // GMT+02:00
	"ROU": 7200,   // GMT+02:00
	"HUN": 3600,   // GMT+01:00
	"POL": 3600,   // GMT+01:00
	"CZE": 3600,   // GMT+01:00
	"SVK": 3600,   // GMT+01:00
	"AUT": 3600,   // GMT+01:00
	"CHE": 3600,   // GMT+01:00
	"LIE": 3600,   // GMT+01:00
	"BEL": 3600,   // GMT+01:00
	"NLD": 3600,   // GMT+01:00
	"LUX": 3600,   // GMT+01:00
	"DNK": 3600,   // GMT+01:00
	"NOR": 3600,   // GMT+01:00
	"SWE": 3600,   // GMT+01:00
	"FIN": 7200,   // GMT+02:00
	"EST": 7200,   // GMT+02:00
	"LVA": 7200,   // GMT+02:00
	"LTU": 7200,   // GMT+02:00
	"BLR": 10800,  // GMT+03:00
	"UKR": 7200,   // GMT+02:00
	"MDA": 7200,   // GMT+02:00
	"ALB": 3600,   // GMT+01:00
	"MKD": 3600,   // GMT+01:00
	"SRB": 3600,   // GMT+01:00
	"MNE": 3600,   // GMT+01:00
	"BIH": 3600,   // GMT+01:00
	"HRV": 3600,   // GMT+01:00
	"SVN": 3600,   // GMT+01:00
	"PRT": 0,      // GMT+00:00
	"IRL": 0,      // GMT+00:00
	"ISL": 0,      // GMT+00:00
	"GRL": -10800, // GMT-03:00
	"CUB": -18000, // GMT-05:00
	"DOM": -14400, // GMT-04:00
	"HTI": -18000, // GMT-05:00
	"JAM": -18000, // GMT-05:00
	"PRI": -14400, // GMT-04:00
	"VIR": -14400, // GMT-04:00
	"BRB": -14400, // GMT-04:00
	"TTO": -14400, // GMT-04:00
	"GUY": -14400, // GMT-04:00
	"SUR": -10800, // GMT-03:00
	"ECU": -18000, // GMT-05:00
	"COL": -18000, // GMT-05:00
	"VEN": -14400, // GMT-04:00
	"PER": -18000, // GMT-05:00
	"BOL": -14400, // GMT-04:00
	"PRY": -14400, // GMT-04:00
	"CHL": -14400, // GMT-04:00
	"URY": -10800, // GMT-03:00
	"GTM": -21600, // GMT-06:00
	"SLV": -21600, // GMT-06:00
	"HND": -21600, // GMT-06:00
	"NIC": -21600, // GMT-06:00
	"CRI": -21600, // GMT-06:00
	"PAN": -18000, // GMT-05:00
	"BLZ": -21600, // GMT-06:00
	"JEY": 0,      // GMT+00:00
	"GGY": 0,      // GMT+00:00
	"IMN": 0,      // GMT+00:00
	"FLK": -10800, // GMT-03:00
	"SGS": -7200,  // GMT-02:00
	"ATF": 18000,  // GMT+05:00
	"MYT": 10800,  // GMT+03:00
	"REU": 14400,  // GMT+04:00
	"COM": 10800,  // GMT+03:00
	"MUS": 14400,  // GMT+04:00
	"MDV": 18000,  // GMT+05:00
	"SYC": 14400,  // GMT+04:00
	"LSO": 7200,   // GMT+02:00
	"SWZ": 7200,   // GMT+02:00
	"NAM": 3600,   // GMT+01:00
	"BWA": 7200,   // GMT+02:00
	"ZWE": 7200,   // GMT+02:00
	"ZMB": 7200,   // GMT+02:00
	"MWI": 7200,   // GMT+02:00
	"MOZ": 7200,   // GMT+02:00
	"AGO": 3600,   // GMT+01:00
	"COG": 3600,   // GMT+01:00
	"COD": 3600,   // GMT+01:00
	"GAB": 3600,   // GMT+01:00
	"GNQ": 3600,   // GMT+01:00
	"CAF": 3600,   // GMT+01:00
	"TCD": 3600,   // GMT+01:00
	"SSD": 7200,   // GMT+02:00
	"ETH": 10800,  // GMT+03:00
	"ERI": 10800,  // GMT+03:00
	"DJI": 10800,  // GMT+03:00
	"SOM": 10800,  // GMT+03:00
	"KEN": 10800,  // GMT+03:00
	"UGA": 10800,  // GMT+03:00
	"RWA": 7200,   // GMT+02:00
	"BDI": 7200,   // GMT+02:00
	"TZA": 10800,  // GMT+03:00
}

var fields = []string{
	"bid_id",
	"response_time",
	"dt",
	"hour",
	"channel_id",
	"ip",
	"user_agent",
	"country",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"device_id",
	"app_id",
	"app_name",
	"ad_id",
	"composite_material_id",
	"material_id_type",
	"title_id",
	"description_id",
	"bid_price",
	"ad_creator_user_id",
	"delivery_type",
	"ad_slot_type",
	"tag_id",
	"timezone",
	"account_id",
	"project_id",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"traffic_material_size",
	"device_model",
	"android_id",
	"delivery_package",
	"impression_time",
}

const Oppo1004470 = "oppo-1004470"

func convertCSV(lines [][]string) []map[string]string {
	ret := make([]map[string]string, 0, len(lines))
	for _, line := range lines {
		m := make(map[string]string)
		for i, t := range line {
			m[fields[i]] = t
		}
		ret = append(ret, m)
	}
	return ret
}
