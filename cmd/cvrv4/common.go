package main

import (
	"bufio"
	"crypto/md5"
	"encoding/csv"
	"encoding/hex"
	"fmt"
	"os"
	"strings"
)

func readApplist(name string) map[string]int {
	file, err := os.Open(name)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return nil
	}
	defer file.Close()

	m := make(map[string]int)
	scanner := bufio.NewScanner(file)
	i := 0
	for scanner.Scan() {
		line := scanner.Text()
		l := strings.Split(line, "\t")
		if len(l) == 2 {
			m[l[0]] = i
			i++
		}
	}

	// 检查是否有错误发生
	if err := scanner.Err(); err != nil {
		fmt.Println("读取文件时发生错误:", err)
	}
	return m
}

// 计算 MD5 Hash
func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// 加载 CSV 文件
func loadCSV(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '\t' // TSV 文件
	return reader.ReadAll()
}

var fields = []string{
	"bid_id", "impression_time", "dt", "hour", "device_id", "device_id_type",
	"device_brand", "device_model", "device_type", "os", "os_version", "country",
	"network_type", "ip", "tag_id", "delivery_type", "traffic_material_size",
	"app_id", "ad_id", "material_id_type", "title_id", "description_id",
	"delivery_material_size", "image_material_id", "video_material_id",
	"package_name", "install_list", "install_list_user", "user_agent",
	"channel_id", "creative_id", "advertiser_type", "user_id", "timezone",
	"account_id", "project_id", "response_timestamp", "click_time", "click_unique_id", "cost",
	"conversion_time", "conversion_type", "conversion_price",
}

func convertCSV(lines [][]string) []map[string]string {
	ret := make([]map[string]string, 0, len(lines))
	for _, line := range lines {
		m := make(map[string]string)
		for i, t := range line {
			m[fields[i]] = t
		}
		if m["click_time"] != "" {
			ret = append(ret, m)
		}
	}
	return ret
}

func convertUid(lines [][]string) map[string]string {
	m := make(map[string]string)
	for _, line := range lines {
		m[line[0]] = line[1]
	}
	return m
}

// 生成 CTR 特征
type Feature struct {
	Feasign    string
	ShowStats  float32
	ClickStats float32
}

type Isotonic struct {
	X []float64 `json:"X"`
	Y []float64 `json:"y"`
}

// 线性插值预测函数
func (m *Isotonic) Predict(x float64) float64 {
	if x <= m.X[0] {
		return m.Y[0]
	}
	if x >= m.X[len(m.X)-1] {
		return m.Y[len(m.Y)-1]
	}

	for i := 0; i < len(m.X)-1; i++ {
		if x >= m.X[i] && x <= m.X[i+1] {
			x0, x1 := m.X[i], m.X[i+1]
			y0, y1 := m.Y[i], m.Y[i+1]
			t := (x - x0) / (x1 - x0)
			return y0 + t*(y1-y0)
		}
	}
	return 0
}
