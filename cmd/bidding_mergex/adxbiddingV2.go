package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"log"
	"os"
	"strconv"
)

var catfields = []string{
	"ad_place_id",
	"ad_position",
	"plat_account_id",
	"dsp_place_id",
	"tag_id",
	"price",
	"publisher_id",
	"app_bundle",
	"app_name",
	"app_version",
	"os",
	"os_version",
	"brand",
	"model",
	"carrier",
	"connection_type",
	"device_type",
	"screen_width",
	"screen_height",
	"province",
	"city",
	"plat_id",
	"traffic_place_id",
	"is_third_budget",
	"advertiser_id",
	"app_nm",
	"app_bundle_name",
	"creative_type",
	"adslot_width",
	"adslot_height",
	"creative_width",
	"creative_height",
	"traffic_type",
	"day_of_week",
	"event_hour",
	"dsp_num",
}

var catfieldsNoPrice = []string{
	"ad_place_id",
	"ad_position",
	"plat_account_id",
	"dsp_place_id",
	"tag_id",
	"publisher_id",
	"app_bundle",
	"app_name",
	"app_version",
	"os",
	"os_version",
	"brand",
	"model",
	"carrier",
	"connection_type",
	"device_type",
	"screen_width",
	"screen_height",
	"province",
	"city",
	"plat_id",
	"traffic_place_id",
	"is_third_budget",
	"advertiser_id",
	"app_nm",
	"app_bundle_name",
	"creative_type",
	"adslot_width",
	"adslot_height",
	"creative_width",
	"creative_height",
	"traffic_type",
	"day_of_week",
	"event_hour",
	"dsp_num",
}

var confFileds = []string{
	"request_id",
	"is_impression",
	"ad_place_id",
	"ad_position",
	"plat_account_id",
	"dsp_place_id",
	"tag_id",
	"price",
	"publisher_id",
	"app_bundle",
	"app_name",
	"app_version",
	"os",
	"os_version",
	"brand",
	"model",
	"carrier",
	"connection_type",
	"device_type",
	"screen_width",
	"screen_height",
	"province",
	"city",
	"plat_id",
	"traffic_place_id",
	"is_third_budget",
	"advertiser_id",
	"app_nm",
	"app_bundle_name",
	"creative_type",
	"adslot_width",
	"adslot_height",
	"creative_width",
	"creative_height",
	"traffic_type",
	"day_of_week",
	"event_hour",
	"dsp_num",
}

var categoryMap catTable

var featureMap = make(map[string]Feature)

var priorCTR float32
var alpha float32
var beta float32

var booster gl.Booster
var fastConfig gl.FastConfig

func main() {

	gl.InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./demo/adxbidv2/model.bin"
	iter := int32(0)
	cErr = gl.BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}
	var numFeatures int32
	cErr = gl.BoosterGetNumFeature(booster, &numFeatures)
	fmt.Printf("model features num: %d\n", numFeatures)

	Categories_str, err := ReadPandasCategorical(modelFile)
	if err != nil {
		log.Fatal(err)
	}

	category := Categories{}
	json.Unmarshal([]byte(Categories_str), &category)
	categoryMap = make(catTable)
	for i, v := range catfieldsNoPrice {
		if v == "price" {
			continue
		}
		categoryMap[v] = make(map[string]float64)
		for j, val := range category[i] {
			switch val := val.(type) {
			case string:
				categoryMap[v][val] = float64(j)
			case int, int32, int64, float32, float64:
				categoryMap[v][fmt.Sprintf("%v", val)] = float64(j)
			default:
				// 其他类型处理（如果需要）
			}
		}
	}

	logData, _ := loadCSV("./demo/adxbidv2/line10")
	logDataM := convertCSV(logData, true)
	statsData, _ := loadCSV("./demo/adxbidv2/merge_stats.txt")

	//for _, row := range statsData {
	//	show, _ := strconv.Atoi(row[1])
	//	click, _ := strconv.Atoi(row[2])
	//	featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	//}
	//
	priorCTR = 0.01 // 先验CTR
	for _, row := range statsData {
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1], 64)
			globalClick, _ := strconv.ParseFloat(row[2], 64)
			priorCTR = float32(globalClick) / float32(globalShow)
		}
	}
	//
	alpha, beta = 0.1, 500.0
	//
	//cErr = gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, 0, -1, gl.Dtype_float32, numFeatures, "", &fastConfig)
	//if cErr != 0 {
	//	fmt.Println("Error:", cErr)
	//	return
	//}
	var out float64
	var outLen64 int64
	//data := make([]float32, numFeatures)
	//data[12] = 10
	//cErr = gl.BoosterPredictForMatSingleRowFast(fastConfig, data, &outLen64, &out)

	//bidmodel := NewBidding(booster, data, featureMap)
	//for i := 100; i > 0; i-- {
	//	r := bidmodel.predictBiddingWinRate(float32(i))
	//	fmt.Printf(" %d rate: %.4f\n", i, r)
	//}
	//
	//for i := 100; i > 0; i-- {
	//	t := bidmodel.GoldenSectionSearch(float32(i))
	//	fmt.Printf("bid is %d, Optimal bid: %.4f\n", i, t)
	//}

	feas, _, ids := generateFeaturesBid(logDataM, statsData, catfields)
	var outData []float64
	//var price []float64

	cErr = gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, 0, -1, gl.Dtype_float32, numFeatures, "", &fastConfig)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return
	}

	for _, v := range feas {
		//bidmodel.SetFea(v)
		//oriprice = append(oriprice, math.Round(float64(v[12])))
		//r := bidmodel.GoldenSectionSearch(float32(v[12]))
		//price = append(price, math.Round(float64(r)))
		gl.BoosterPredictForMatSingleRowFast(fastConfig, v, &outLen64, &out)
		outData = append(outData, out)
	}
	outputFile, _ := os.Create("./demo/adxbidv2/predictions_demo.txt")
	defer outputFile.Close()
	writer := csv.NewWriter(outputFile)
	writer.Comma = '\t'
	writer.Write([]string{"request_id", "value"})
	for i, bidID := range ids {
		writer.Write([]string{bidID, fmt.Sprintf("%f", outData[i])})
	}
	writer.Flush()

	cErr = gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, 0, -1, gl.Dtype_float32, numFeatures, "", &fastConfig)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return
	}
	//data := make([]float32, numFeatures)
	//data[12] = 10
	//cErr = gl.BoosterPredictForMatSingleRowFast(fastConfig, data, &outLen64, &out)

	sum := 0.0
	hit := 0.0
	for n, v := range feas {
		bidmodel := NewBidding(booster, v, featureMap)
		now, pre := 0.0, 0.0
		for i := 1; i <= 1000; i++ {
			r := bidmodel.predictBiddingWinRate(float32(i))
			//fmt.Printf(" %d rate: %.6f\n", i, r)
			pre = now
			now = r
			//if pre > 0 && now/pre > 3 {
			//	fmt.Printf("num is %d, bid is %d, win rate: %.4f; pre bid is %d, win rate: %4f\n", n, i, now, i-1, pre)
			//}
			sum++
			if now-pre < 0 {
				hit++
				fmt.Printf("num is %d, bid is %d, win rate: %.4f; pre bid is %d, win rate: %4f\n", n, i, now, i-1, pre)
			}
		}
	}
	fmt.Printf("hit rate: %.4f\n", hit/sum)

	//bidmodel := NewBidding(booster, feas[98], featureMap)
	//for i := 1; i <= 1000; i++ {
	//	r := bidmodel.predictBiddingWinRate(float32(i))
	//	fmt.Printf(" %d rate: %.6f\n", i, r)
	//}
	//
	//for i := 100; i > 0; i-- {
	//	t := bidmodel.GoldenSectionSearch(float32(i))
	//	fmt.Printf("bid is %d, Optimal bid: %.4f\n", i, t)
	//}

}

func generateFeaturesBid(logData []map[string]string, statsData [][]string, features []string) ([][]float32, []int, []string) {
	//['feasign', 'show_stats', 'click_stats', 'cv_stats']
	for _, row := range statsData {
		show, err := strconv.Atoi(row[1])
		if err != nil {
			continue
		}
		click, _ := strconv.Atoi(row[2])
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	}

	var featureMatrix [][]float32
	var clicks []int
	var bidIDs []string
	featureMatrix = make([][]float32, len(logData))
	var (
		// 标签
		cat    = make([]float32, 0, len(features))
		show   = make([]float32, 0, len(features))
		click  = make([]float32, 0, len(features))
		ctrval = make([]float32, 0, len(features))
	)

	for i, row := range logData {

		//layout := "2006-01-02 15:04:05"
		//impressionTime, _ := time.Parse(layout, row["response_time"])
		//loc := time.FixedZone("Custom", timezoneOffsets[row["country"]])
		//impressionTime = impressionTime.In(loc)
		//dayOfWeek := impressionTime.Weekday().String()[:3]
		//eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
		//row["day_of_week"] = dayOfWeek
		//row["event_hour"] = eventHour
		//var featureRow []float64
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]

		for _, feature := range features {

			if feature == "price" {
				bid, _ := strconv.ParseFloat(row[feature], 64)
				cat = append(cat, float32(bid))
			} else {
				cat = append(cat, float32(categoryMap.GetVal(feature, row[feature])))
			}

			feasign := md5Hash(feature + "::" + row[feature])
			stat, exists := featureMap[feasign]
			if !exists {
				show = append(show, 0)
				click = append(click, 0)
			} else {
				show = append(show, stat.ShowStats)
				click = append(click, stat.ClickStats)
			}

			if !exists || stat.ShowStats < 10 {
				ctr := (stat.ClickStats + float32(alpha*beta*priorCTR)) / (stat.ShowStats + float32(alpha*beta))
				//featureRow = append(featureRow, (ctr))
				ctrval = append(ctrval, (ctr))
			} else {
				//featureRow = append(featureRow, (stat.ClickStats / stat.ShowStats))
				ctrval = append(ctrval, (stat.ClickStats / stat.ShowStats))
			}
		}

		featureMatrix[i] = append(featureMatrix[i], cat...)
		featureMatrix[i] = append(featureMatrix[i], show...)
		featureMatrix[i] = append(featureMatrix[i], click...)
		featureMatrix[i] = append(featureMatrix[i], ctrval...)
		for _, v := range featureMatrix {
			fmt.Printf("%f\t", v)
		}

		fmt.Printf("\n")
		bidIDs = append(bidIDs, row["request_id"])
		//if row["request_id"] == "10170-d09dda06-ae8e-4ae8-b016-dfa4fe53ca8f" {
		//	b, _ := json.Marshal(row)
		//	println(string(b))
		//}

	}

	return featureMatrix, clicks, bidIDs
}
