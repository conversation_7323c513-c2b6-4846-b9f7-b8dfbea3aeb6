package main

import (
	"bufio"
	"crypto/md5"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"gopkg.in/yaml.v3"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

var appIndex map[string]int
var categoryMap catTable

var needCatVal map[string]bool

const Oppo1004470 = "oppo-1004470"

func readApplist(name string) map[string]int {
	file, err := os.Open(name)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return nil
	}
	defer file.Close()

	m := make(map[string]int)
	scanner := bufio.NewScanner(file)
	i := 0
	for scanner.Scan() {
		line := scanner.Text()
		l := strings.Split(line, "\t")
		if len(l) == 2 {
			m[l[0]] = i
			i++
		}
	}

	// 检查是否有错误发生
	if err := scanner.Err(); err != nil {
		fmt.Println("读取文件时发生错误:", err)
	}
	return m
}

// 计算 MD5 Hash
func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// 加载 CSV 文件
func loadCSV(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '\t' // TSV 文件
	return reader.ReadAll()
}

var fields = []string{
	"bid_id", "impression_time", "dt", "hour", "device_id", "device_id_type",
	"device_brand", "device_model", "device_type", "os", "os_version", "country",
	"network_type", "ip", "tag_id", "delivery_type", "traffic_material_size",
	"app_id", "ad_id", "material_id_type", "title_id", "description_id",
	"delivery_material_size", "image_material_id", "video_material_id",
	"package_name", "install_list", "install_list_user", "user_agent",
	"channel_id", "creative_id", "advertiser_type", "user_id", "timezone",
	"account_id", "project_id", "click_time", "click_unique_id", "cost",
	"conversion_time", "conversion_type", "conversion_price",
}

// 需要改为map
func convertCSV(lines [][]string) []map[string]string {
	ret := make([]map[string]string, 0, len(lines))
	for _, line := range lines {
		m := make(map[string]string)
		for i, t := range line {
			m[fields[i]] = t
		}
		if m["tag_id"] == Oppo1004470 {
			ret = append(ret, m)
		}
	}
	return ret
}

// 生成 CTR 特征
type Feature struct {
	Feasign    string
	ShowStats  float32
	ClickStats float32
}

func generateFeatures(logData []map[string]string, statsData [][]string, features []string) ([]float64, []int, []string) {
	featureMap := make(map[string]Feature)
	//['feasign', 'show_stats', 'click_stats', 'cv_stats']
	for _, row := range statsData {
		show, _ := strconv.Atoi(row[1])
		click, _ := strconv.Atoi(row[2])
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	}

	var featureMatrix []float64
	var clicks []int
	var bidIDs []string
	priorCTR := 0.01 // 先验CTR
	for _, row := range statsData {
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1], 64)
			globalClick, _ := strconv.ParseFloat(row[2], 64)
			priorCTR = globalClick / globalShow
		}
	}

	alpha, beta := 0.1, 500.0

	for _, row := range logData {
		layout := "2006-01-02 15:04:05"
		impressionTime, _ := time.Parse(layout, row["impression_time"])
		loc := time.FixedZone("Custom", timezoneOffsets[row["country"]])
		impressionTime = impressionTime.In(loc)
		dayOfWeek := impressionTime.Weekday().String()[:3]
		eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
		row["day_of_week"] = dayOfWeek
		row["event_hour"] = eventHour
		var featureRow []float32
		for _, feature := range features {
			if needCatVal[feature] {
				if feature == "event_hour" {
					eh := strings.TrimPrefix(row[feature], "0")
					featureRow = append(featureRow, categoryMap.GetVal(feature, eh))
				} else {
					featureRow = append(featureRow, categoryMap.GetVal(feature, row[feature]))
				}
				//featureRow = append(featureRow, -1)
			}

			feasign := md5Hash(feature + "::" + row[feature])
			stat, exists := featureMap[feasign]
			if !exists {
				featureRow = append(featureRow, 0)
				featureRow = append(featureRow, 0)
			} else {
				featureRow = append(featureRow, stat.ShowStats)
				featureRow = append(featureRow, stat.ClickStats)
			}

			if !exists || stat.ShowStats < 10 {
				ctr := (stat.ClickStats + alpha*beta*priorCTR) / (stat.ShowStats + alpha*beta)
				featureRow = append(featureRow, (ctr))
			} else {
				featureRow = append(featureRow, (stat.ClickStats / stat.ShowStats))
			}
		}
		install := make([]float64, len(appIndex))
		for _, v := range strings.Split(row["install_list_user"], ",") {
			if n, ok := appIndex[v]; ok {
				install[n] = 1
			}
		}
		//for i := range install {
		//	install[i] = 1
		//}

		click := 0
		if row["click_time"] != "" { // click_time 是否为空
			click = 1
		}
		featureMatrix = append(featureMatrix, featureRow...)
		featureMatrix = append(featureMatrix, install...)
		//for _, v := range featureMatrix {
		//	fmt.Printf("%f\n", v)
		//}

		clicks = append(clicks, click)
		bidIDs = append(bidIDs, row["bid_id"])
		//break
	}

	return featureMatrix, clicks, bidIDs
}

func main6() {
	loc := time.FixedZone("Custom", timezoneOffsets["USA"])
	utcTime := time.Now().UTC()
	utcTime = utcTime.In(loc)

	dayOfWeek := utcTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", utcTime.Hour())

	fmt.Println(utcTime)
	fmt.Println(dayOfWeek)
	fmt.Println(eventHour)

	Categories_str, err := ReadPandasCategorical("./demo/v2/local_ctr_model.txt")
	fmt.Println(Categories_str)

	category := Categories{}
	json.Unmarshal([]byte(Categories_str), &category)

	gl.InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./demo/v2/local_ctr_model.txt"
	iter := int32(0)
	var booster gl.Booster
	cErr = gl.BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}
	var numFeatures int32
	cErr = gl.BoosterGetNumFeature(booster, &numFeatures)
	fmt.Printf("model features num: %d\n", numFeatures)

	configs, err := loadOrderedYAML("./demo/v2/ctr_fealist.yaml")
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	var features []string
	//var featuresPrint []string
	var catFeatures []string
	// **按顺序打印**

	needCatVal = make(map[string]bool)
	for _, feature := range configs {
		//fmt.Printf("Feature: %s\n", feature.Key)
		//fmt.Printf("  Enable: %v\n", feature.Enable)
		//fmt.Printf("  Value: %d\n", feature.Value)
		//fmt.Printf("  Method: %s\n", feature.FeatureProcessing.Method)
		//fmt.Printf("  Params: %v\n", feature.FeatureProcessing.Params)
		//fmt.Printf("  RequiresStatistics: %v\n\n", feature.RequiresStatistics)
		if feature.Enable {
			features = append(features, feature.Key)
			//featuresPrint = append(featuresPrint, fmt.Sprintf("\"%s\"", feature.Key))
			if feature.Value < 1000 {
				catFeatures = append(catFeatures, feature.Key)
				needCatVal[feature.Key] = true
			}
		}
	}
	fmt.Println(features)
	//fmt.Println(strings.Join(featuresPrint, ","))
	fmt.Println(strings.Join(catFeatures, ","))
	fmt.Println(len(catFeatures))
	categoryMap = make(catTable)
	for i, v := range catFeatures {
		categoryMap[v] = make(map[string]float64)
		for j, val := range category[i] {
			switch val := val.(type) {
			case string:
				categoryMap[v][val] = float64(j)
			case int, int32, int64, float32, float64:
				categoryMap[v][fmt.Sprintf("%v", val)] = float64(j)
			default:
				// 其他类型处理（如果需要）
			}
		}
	}

	appIndex = readApplist("./demo/v2/install_list.txt")
	//加载日志数据和统计特征数据
	logData, err := loadCSV("./demo/v2/shitu.csv")
	if err != nil {
		panic(err)
	}
	logDataM := convertCSV(logData)
	//b, _ := json.Marshal(logDataM)
	//fmt.Println(string(b))

	statsData, _ := loadCSV("./demo/v2/feature_sign.txt")

	// 生成特征和标签
	featureMatrix, clicks, bidIDs := generateFeatures(logDataM, statsData, features)

	fmt.Println(len(featureMatrix))
	fmt.Println(len(clicks))
	fmt.Println(len(bidIDs))

	//featureMatrix = append(featureMatrix, featureMatrix...)
	//featureMatrix = featureMatrix[:numFeatures*500]

	nRaw, nCol := int32(len(featureMatrix)/int(numFeatures)), numFeatures
	// 注意这个要提前分配内存
	outval := make([]float64, nRaw)
	var outLen64 int64
	start := time.Now()
	for i := 0; i < 1; i++ {
		gl.BoosterPredictForMat(booster, featureMatrix, gl.Dtype_float64, nRaw, nCol, gl.Row_major, gl.PredictNormal,
			0, -1, "", &outLen64, outval)
	}
	last := time.Since(start)
	fmt.Println("last time:", last)
	//gl.BoosterPredictForMat(booster, featureMatrix, gl.Dtype_float32, nRaw, nCol, gl.Row_major, gl.PredictNormal,
	//	0, -1, "", &outLen64, outval)
	fmt.Printf("outlen %d\n", outLen64)
	//for i, v := range outval {
	//	fmt.Printf("outval %d, data %f\n", i, v)
	//}
	fmt.Printf("outval %f\n", outval[0])
	//保存结果
	outputFile, _ := os.Create("./demo/v2/predictions_demo.txt")
	defer outputFile.Close()
	writer := csv.NewWriter(outputFile)
	writer.Comma = '\t'
	writer.Write([]string{"bid_id", "y_true", "y_pred"})
	for i, bidID := range bidIDs {
		writer.Write([]string{bidID, strconv.Itoa(clicks[i]), fmt.Sprintf("%f", outval[i])})
	}
	writer.Flush()
}

// 结构体定义
type FeatureProcessing struct {
	Method string            `yaml:"method"`
	Params map[string]string `yaml:"params"`
}

type FeatureConfig struct {
	Key                string            // 额外存储键名
	Enable             bool              `yaml:"enable"`
	Value              int               `yaml:"value"`
	FeatureProcessing  FeatureProcessing `yaml:"feature_processing"`
	RequiresStatistics bool              `yaml:"requires_statistics"`
}

// 自定义解析函数，保证顺序
// **使用 yaml.Node 保持顺序**
func loadOrderedYAML(filename string) ([]FeatureConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var rootNode yaml.Node
	err = yaml.Unmarshal(data, &rootNode)
	if err != nil {
		return nil, err
	}

	// **如果是 DocumentNode，进入它的子节点**
	if rootNode.Kind == yaml.DocumentNode && len(rootNode.Content) > 0 {
		rootNode = *rootNode.Content[0] // 进入根 MappingNode
	}

	// **确保是 MappingNode**
	if rootNode.Kind != yaml.MappingNode {
		return nil, fmt.Errorf("expected MappingNode, got %v", rootNode.Kind)
	}

	// **遍历 MappingNode 保持顺序**
	var orderedConfigs []FeatureConfig
	for i := 0; i < len(rootNode.Content); i += 2 {
		keyNode := rootNode.Content[i]     // YAML 的 key
		valueNode := rootNode.Content[i+1] // YAML 的 value

		var feature FeatureConfig
		err := valueNode.Decode(&feature) // 解析 value 部分
		if err != nil {
			return nil, err
		}
		feature.Key = keyNode.Value // 记录 key 名称
		orderedConfigs = append(orderedConfigs, feature)
	}

	return orderedConfigs, nil
}
