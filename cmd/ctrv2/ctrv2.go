package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

func generateFeaturesV2(logData []map[string]string, statsData [][]string, features []string) ([]float32, []int, []string) {
	featureMap := make(map[string]Feature)
	//['feasign', 'show_stats', 'click_stats', 'cv_stats']
	for _, row := range statsData {
		show, _ := strconv.Atoi(row[1])
		click, _ := strconv.Atoi(row[2])
		featureMap[row[0]] = Feature{Feasign: row[0], ShowStats: float32(show), ClickStats: float32(click)}
	}

	var featureMatrix []float32
	var clicks []int
	var bidIDs []string
	priorCTR := 0.01 // 先验CTR
	for _, row := range statsData {
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1], 64)
			globalClick, _ := strconv.ParseFloat(row[2], 64)
			priorCTR = globalClick / globalShow
		}
	}

	alpha, beta := 0.1, 500.0

	var (
		cat    = make([]float32, 0, len(features))
		show   = make([]float32, 0, len(features))
		click  = make([]float32, 0, len(features))
		ctrval = make([]float32, 0, len(features))
	)

	for _, row := range logData {
		layout := "2006-01-02 15:04:05"
		impressionTime, _ := time.Parse(layout, row["impression_time"])
		loc := time.FixedZone("Custom", timezoneOffsets[row["country"]])
		impressionTime = impressionTime.In(loc)
		dayOfWeek := impressionTime.Weekday().String()[:3]
		eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
		row["day_of_week"] = dayOfWeek
		row["event_hour"] = eventHour
		//var featureRow []float64
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]

		for _, feature := range features {
			if needCatVal[feature] {
				//if feature == "event_hour" {
				//	eh := strings.TrimPrefix(row[feature], "0")
				//	cat = append(cat, categoryMap.GetVal(feature, eh))
				//} else {
				cat = append(cat, float32(categoryMap.GetVal(feature, row[feature])))
				//}
				//featureRow = append(featureRow, -1)
			}

			feasign := md5Hash(feature + "::" + row[feature])
			stat, exists := featureMap[feasign]
			if !exists {
				show = append(show, 0)
				click = append(click, 0)
			} else {
				show = append(show, stat.ShowStats)
				click = append(click, stat.ClickStats)
			}

			if !exists || stat.ShowStats < 10 {
				ctr := (stat.ClickStats + float32(alpha*beta*priorCTR)) / (stat.ShowStats + float32(alpha*beta))
				ctrval = append(ctrval, (ctr))
			} else {
				ctrval = append(ctrval, (stat.ClickStats / stat.ShowStats))
			}
		}
		install := make([]float32, len(appIndex))
		for _, v := range strings.Split(row["install_list_user"], ",") {
			if n, ok := appIndex[v]; ok {
				install[n] = 1
			}
		}
		//for i := range install {
		//	install[i] = 1
		//}

		is_click := 0
		if row["click_time"] != "" { // click_time 是否为空
			is_click = 1
		}
		featureMatrix = append(featureMatrix, cat...)
		featureMatrix = append(featureMatrix, show...)
		featureMatrix = append(featureMatrix, click...)
		featureMatrix = append(featureMatrix, ctrval...)
		featureMatrix = append(featureMatrix, install...)
		//for _, v := range featureMatrix {
		//	fmt.Printf("%f\n", v)
		//}

		clicks = append(clicks, is_click)
		bidIDs = append(bidIDs, row["bid_id"])
		//break
		//b, _ := json.Marshal(row)
		//fmt.Println(string(b))
		//break
	}

	return featureMatrix, clicks, bidIDs
}

func main5() {
	loc := time.FixedZone("Custom", timezoneOffsets["USA"])
	utcTime := time.Now().UTC()
	utcTime = utcTime.In(loc)

	dayOfWeek := utcTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", utcTime.Hour())

	fmt.Println(utcTime)
	fmt.Println(dayOfWeek)
	fmt.Println(eventHour)

	Categories_str, err := ReadPandasCategorical("./demo/v2/local_ctr_model.txt")
	fmt.Println(Categories_str)

	category := Categories{}
	json.Unmarshal([]byte(Categories_str), &category)

	gl.InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./demo/v2/local_ctr_model.txt"
	iter := int32(0)
	var booster gl.Booster
	cErr = gl.BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}
	var numFeatures int32
	cErr = gl.BoosterGetNumFeature(booster, &numFeatures)
	fmt.Printf("model features num: %d\n", numFeatures)

	configs, err := loadOrderedYAML("./demo/v2/ctr_fealist.yaml")
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	var features []string
	//var featuresPrint []string
	var catFeatures []string
	// **按顺序打印**

	needCatVal = make(map[string]bool)
	for _, feature := range configs {
		//fmt.Printf("Feature: %s\n", feature.Key)
		//fmt.Printf("  Enable: %v\n", feature.Enable)
		//fmt.Printf("  Value: %d\n", feature.Value)
		//fmt.Printf("  Method: %s\n", feature.FeatureProcessing.Method)
		//fmt.Printf("  Params: %v\n", feature.FeatureProcessing.Params)
		//fmt.Printf("  RequiresStatistics: %v\n\n", feature.RequiresStatistics)
		if feature.Enable {
			features = append(features, feature.Key)
			//featuresPrint = append(featuresPrint, fmt.Sprintf("\"%s\"", feature.Key))
			if feature.Value < 1000 {
				catFeatures = append(catFeatures, feature.Key)
				needCatVal[feature.Key] = true
			}
		}
	}
	fmt.Println(features)
	//fmt.Println(strings.Join(featuresPrint, ","))
	fmt.Println(strings.Join(catFeatures, ","))
	fmt.Println(len(catFeatures))
	categoryMap = make(catTable)
	for i, v := range catFeatures {
		categoryMap[v] = make(map[string]float64)
		for j, val := range category[i] {
			switch val := val.(type) {
			case string:
				categoryMap[v][val] = float64(j)
			case int, int32, int64, float32, float64:
				categoryMap[v][fmt.Sprintf("%v", val)] = float64(j)
			default:
				// 其他类型处理（如果需要）
			}
		}
	}

	appIndex = readApplist("./demo/v2/install_list.txt")
	//加载日志数据和统计特征数据
	logData, err := loadCSV("./demo/v2/shitu.csv")
	if err != nil {
		panic(err)
	}
	logDataM := convertCSV(logData)
	//b, _ := json.Marshal(logDataM)
	//fmt.Println(string(b))

	statsData, _ := loadCSV("./demo/v2/feature_sign.txt")

	// 生成特征和标签
	featureMatrix, clicks, bidIDs := generateFeaturesV2(logDataM, statsData, features)

	fmt.Println(len(featureMatrix))
	fmt.Println(len(clicks))
	fmt.Println(len(bidIDs))

	//featureMatrix = append(featureMatrix, featureMatrix...)
	//featureMatrix = featureMatrix[:numFeatures*500]

	nRaw, nCol := int32(len(featureMatrix)/int(numFeatures)), numFeatures
	// 注意这个要提前分配内存
	outval := make([]float64, nRaw)
	var outLen64 int64
	start := time.Now()
	for i := 0; i < 1; i++ {
		gl.BoosterPredictForMat(booster, featureMatrix, gl.Dtype_float32, nRaw, nCol, gl.Row_major, gl.PredictNormal,
			0, -1, "", &outLen64, outval)
	}
	last := time.Since(start)
	fmt.Println("last time:", last)
	//gl.BoosterPredictForMat(booster, featureMatrix, gl.Dtype_float32, nRaw, nCol, gl.Row_major, gl.PredictNormal,
	//	0, -1, "", &outLen64, outval)
	fmt.Printf("outlen %d\n", outLen64)
	//for i, v := range outval {
	//	fmt.Printf("outval %d, data %f\n", i, v)
	//}
	//fmt.Printf("outval %f\n", outval[0])
	nsr := 0.025
	for i, v := range outval {
		outval[i] = v / (v + (1-v)/nsr)
	}
	//保存结果
	outputFile, _ := os.Create("./demo/v2/predictions_demo.txt")
	defer outputFile.Close()
	writer := csv.NewWriter(outputFile)
	writer.Comma = '\t'
	writer.Write([]string{"bid_id", "y_true", "y_pred"})
	for i, bidID := range bidIDs {
		writer.Write([]string{bidID, strconv.Itoa(clicks[i]), fmt.Sprintf("%f", outval[i])})
	}
	writer.Flush()
}
