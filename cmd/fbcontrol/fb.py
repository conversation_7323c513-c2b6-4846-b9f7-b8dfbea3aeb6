"""
CPA约束和预算约束的一价场景下的出价系统
结合PID控制算法进行智能出价
"""
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, Tuple, List, Any
import json
import math
from datetime import datetime, timedelta
import warnings
import optuna
from functools import partial
import pickle
import logging
warnings.filterwarnings('ignore')
import traceback

MIN_BID = 1
ROUND_MINUTES = 30

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s  - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler('logs/fb_control_m.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PIDController:
    """PID控制器"""

    def __init__(self, kp: float = 0.002, ki: float = 0.000001, kd: float = 0.000001):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.error_sum = 0.0
        self.prev_error = 0.0
        self.first_round = True
        self.sec_round = False
        self.min_phi = -100.0
        self.max_phi = 100.0

    def compute(self, error: float, curr_value: float = 0, total_value: float = 0) -> Tuple[float, Dict[str, float]]:
        """计算PID输出，返回phi值和详细的PID状态信息"""
        original_error = error
        if curr_value != 0:
            error *= curr_value

        if self.first_round:
            phi = 0.0
            self.first_round = False
            self.sec_round = True
            error_diff = 0.0
        elif self.sec_round:
            self.error_sum += error
            phi = self.kp * error + self.ki * self.error_sum
            self.sec_round = False
            error_diff = 0.0
        else:
            self.error_sum += error
            error_diff = error - self.prev_error
            phi = (self.kp * error +
                   self.ki * self.error_sum +
                   self.kd * error_diff)

        if total_value != 0:
            phi /= total_value
        logger.debug(f"2phi: {phi}, error: {self.kp}*{error} + {self.ki}*{self.error_sum} + {self.kd}*{error_diff}")

        # TODOTODO限制phi范围
        phi = max(self.min_phi, min(self.max_phi, phi))

        pid_details = {
            'kp': self.kp,
            'error': original_error,
            'ki': self.ki,
            'error_sum': self.error_sum,
            'kd': self.kd,
            'error_diff': error_diff
        }

        self.prev_error = error
        return phi, pid_details


class TrafficAllocator:
    """流量分配器，基于国家流量分布进行预算分配"""

    def __init__(self, traffic_data: pd.DataFrame):
        # 创建数据副本以避免修改原始数据
        self.traffic_data = traffic_data.copy()
        self._prepare_traffic_data()
        # 预计算累计流量数据
        self._precompute_cumulative_traffic()

    def _prepare_traffic_data(self):
        """准备流量数据"""
        self.traffic_data['datetime'] = pd.to_datetime(self.traffic_data['datetime'])
        self.traffic_data['hour'] = self.traffic_data['datetime'].dt.hour
        self.traffic_data['minute'] = self.traffic_data['datetime'].dt.minute
        # 重置索引以避免索引问题
        self.traffic_data = self.traffic_data.reset_index(drop=True)
        # 按国家和时间排序
        self.traffic_data = self.traffic_data.sort_values(['country', 'datetime']).reset_index(drop=True)

    def _precompute_cumulative_traffic(self):
        """预计算每个国家的累计流量数据"""
        logger.info("begin to precompute cumulative traffic data...")
        self.cumulative_traffic = {}

        for country in self.traffic_data['country'].unique():
            country_data = self.traffic_data[self.traffic_data['country'] == country].copy()
            country_data = country_data.sort_values('datetime').reset_index(drop=True)

            country_data['cumulative_pv'] = country_data['pv'].cumsum()

            self.cumulative_traffic[country] = {
                'datetime': country_data['datetime'].to_numpy(),
                'cumulative_pv': country_data['cumulative_pv'].values,
                'total_pv': country_data['pv'].sum()
            }


    def _get_cumulative_traffic_at_time(self, country: str, target_time: pd.Timestamp) -> float:
        """获取指定时间点的累计流量"""
        if country not in self.cumulative_traffic:
            return 0.0

        country_data = self.cumulative_traffic[country]
        datetimes = country_data['datetime']
        cumulative_pvs = country_data['cumulative_pv']

        target_time_np = target_time.to_numpy()

        # 找到最接近目标时间的索引（向下取整）
        # 使用numpy的searchsorted进行二分查找，效率更高
        idx = np.searchsorted(datetimes, target_time_np, side='right') - 1

        if idx < 0:
            return 0.0
        elif idx >= len(cumulative_pvs):
            return cumulative_pvs[-1]
        else:
            return cumulative_pvs[idx]

    def get_traffic_share(self, country: str, start_time: datetime, curr_time: datetime, end_time: datetime, time_delta: int = 7) -> float:
        """获取指定时间段内某国家的流量比例: traffic_start_to_curr / traffic_start_to_end"""
        try:
            if country not in self.cumulative_traffic:
                return 0.0

            last_week_start = start_time - timedelta(days=time_delta)
            last_week_end = end_time - timedelta(days=time_delta)
            last_week_curr = curr_time - timedelta(days=time_delta)

            traffic_at_start = self._get_cumulative_traffic_at_time(country, pd.Timestamp(last_week_start))
            traffic_at_curr = self._get_cumulative_traffic_at_time(country, pd.Timestamp(last_week_curr))
            traffic_at_end = self._get_cumulative_traffic_at_time(country, pd.Timestamp(last_week_end))

            traffic_start_to_curr = traffic_at_curr - traffic_at_start
            traffic_start_to_end = traffic_at_end - traffic_at_start

            if traffic_start_to_end > 0:
                share = traffic_start_to_curr / traffic_start_to_end
            else:
                share = 0.0

            return share

        except Exception as e:
            logger.error(f"Error in get_traffic_share: {traceback.format_exc()}")
            return 0.0

class CPABudgetController:
    """CPA和预算双重约束控制器"""

    def __init__(self, params: Dict[str, Any] = None):
        self.params = params or {}
        self.round_minutes = self.params.get('round_minutes', 60)  # 每轮分钟数
        self.min_bid = self.params.get('min_bid', MIN_BID)

        pid_params = self.params.get('pid_params', {})
        self.ecpa_pid_params = pid_params.get('ecpa', {'kp': 0.002, 'ki': 0.001, 'kd': 0.0001})
        self.budget_pid_params = pid_params.get('budget', {'kp': 0.001, 'ki': 0.0000005, 'kd': 0.0000005})

        self.ecpa_controllers: Dict[str, PIDController] = {}
        self.budget_controllers: Dict[str, PIDController] = {}

        # 存储历史数据
        self.history: Dict[str, Dict] = {}

        self.traffic_history: Dict[str, List] = {}  # 存储每个adid的累计流量份额历史
        self.campaign_total_traffic: Dict[str, float] = {}  # 存储每个adid整个活动期间的总流量份额
        self.avg_spend: Dict[str, float] = {}  # 存储每个adid的理想平均花费速度

    def get_or_create_controllers(self, adid: str) -> Tuple[PIDController, PIDController]:
        """获取或创建指定adid的PID控制器"""
        if adid not in self.ecpa_controllers:
            self.ecpa_controllers[adid] = PIDController(**self.ecpa_pid_params)
            self.budget_controllers[adid] = PIDController(**self.budget_pid_params)
            self.history[adid] = {
                'total_cost': 0.0,
                'total_cv': 0,
                'round_costs': [],
                'round_cvs': [],
                'ecpa_errors': [],
                'budget_errors': [],
                'phi_values': []
            }
            self.traffic_history[adid] = []
        return self.ecpa_controllers[adid], self.budget_controllers[adid]

    def initialize_campaign_traffic(self, adid: str, total_budget: float):
        """初始化活动的总流量份额和理想平均花费速度"""
        if adid not in self.campaign_total_traffic:
            # 假设对应日期的流量份额为1.0
            self.campaign_total_traffic[adid] = 1.0
            # 计算理想的平均花费速度（每单位流量份额应该花费的金额）
            self.avg_spend[adid] = total_budget / self.campaign_total_traffic[adid]

    def update_phi(self, adid: str, reference_cpa: float, reference_spend: float,
                   round_cost: float, round_cv: int, current_traffic_share: float = None) -> Tuple[float, float, Dict[str, Any]]:
        """更新phi参数，返回(ecpa_phi, budget_phi, pid_details)"""
        ecpa_controller, budget_controller = self.get_or_create_controllers(adid)

        # 更新历史数据
        history = self.history[adid]
        history['total_cost'] += round_cost
        history['total_cv'] += round_cv
        history['round_costs'].append(round_cost)
        history['round_cvs'].append(round_cv)

        # 计算eCPA误差
        current_ecpa = history['total_cost'] / history['total_cv'] if history['total_cv'] > 0 else 0
        ecpa_error = reference_cpa - current_ecpa

        # 更新流量历史
        self.traffic_history[adid].append(current_traffic_share)

        if len(self.traffic_history[adid]) >= 2 and current_traffic_share > 0:
            # 计算当前实际花费速度
            current_spend_rate = history['total_cost'] / current_traffic_share
        else:
            current_spend_rate = 0

        budget_error = reference_spend - current_spend_rate

        # 计算phi值和获取详细信息
        #ecpa_phi = ecpa_controller.compute(ecpa_error, round_cv, history['total_cv'])
        ecpa_phi, ecpa_details = ecpa_controller.compute(ecpa_error)
        budget_phi, budget_details = budget_controller.compute(budget_error)

        pid_details = {
            'ecpa_kp': ecpa_details['kp'],
            'ecpa_error': ecpa_details['error'],
            'ecpa_ki': ecpa_details['ki'],
            'ecpa_error_sum': ecpa_details['error_sum'],
            'ecpa_kd': ecpa_details['kd'],
            'ecpa_error_diff': ecpa_details['error_diff'],
            'budget_kp': budget_details['kp'],
            'budget_error': budget_details['error'],
            'budget_ki': budget_details['ki'],
            'budget_error_sum': budget_details['error_sum'],
            'budget_kd': budget_details['kd'],
            'budget_error_diff': budget_details['error_diff']
        }

        history['ecpa_errors'].append(ecpa_error)
        history['budget_errors'].append(budget_error)
        history['phi_values'].append([ecpa_phi, budget_phi])

        return ecpa_phi, budget_phi, pid_details

    def compute_bid(self, ctr: float, cvr: float, ecpa: float, target_cpa: float,
                    ecpa_phi: float, budget_phi: float) -> Tuple[float, bool]:
        """计算出价"""
        # budget的error调整A (基础eCPA)
        # 出价公式1：ctr * cvr * min(A, A_upper)
        ecpa = ecpa * 100
        target_cpa = target_cpa * 100

        A = ecpa * math.exp(budget_phi)
        # ecpa的error调整A_upper (目标CPA)
        A_upper = target_cpa * math.exp(ecpa_phi)

        bid = ctr * cvr * min(A, A_upper)
        is_limited_by_upper = A_upper < A
        """

        # 出价公式2：
        A = ecpa * math.exp(ecpa_phi)
        bid = ctr * cvr * A
        is_limited_by_upper = False

        # 出价公式3：
        #A = ecpa * math.exp(ecpa_phi)
        A = ecpa * math.exp(budget_phi)
        A_upper = target_cpa
        bid = ctr * cvr * min(A, A_upper)
        is_limited_by_upper = A_upper < A
        """

        return int(max(bid * 1000, self.min_bid)), is_limited_by_upper


class OfflineSimulator:
    """离线模拟器"""

    def __init__(self, impression_data: pd.DataFrame, ad_data: pd.DataFrame,
                 traffic_data: pd.DataFrame, controller_params: Dict[str, Any] = None):
        # 创建数据副本以避免修改原始数据
        self.impression_data = impression_data.copy()
        self.ad_data = ad_data.copy()
        self.traffic_data = traffic_data.copy()

        self.controller = CPABudgetController(controller_params)
        self.traffic_allocator = TrafficAllocator(self.traffic_data)

        self.round_minutes = controller_params.get('round_minutes', 60) if controller_params else 60

        self.results = []
        self.round_results = []

    def prepare_data(self):
        """准备数据"""
        self.impression_data = self.impression_data.merge(
            self.ad_data[['adid', 'ecpa', 'target_cpa', 'budget', 'country']],
            on='adid', how='inner'
        )

        # 按时间排序并重置索引
        self.impression_data = self.impression_data.sort_values('time').reset_index(drop=True)

        # 打印cv最大的top5的ad
        top_cv_ads = self.impression_data.groupby('adid')['cv'].sum().nlargest(5)
        logger.info(f"top 5 cv adids: {dict(top_cv_ads)}")

        # 统计整体ecpa
        total_bid_price = self.impression_data['bid_price'].sum()
        total_cv = self.impression_data['cv'].sum()
        overall_ecpa = total_bid_price / 1000 / 100 / total_cv if total_cv > 0 else 0.0
        logger.info(f"impression overall ecpa: {overall_ecpa:.3f}")

    def allocate_budget(self, adid: str, total_budget: float, current_time: datetime,
                        end_time: datetime) -> float:
        """为指定轮次分配预算"""
        ad_info = self.ad_data[self.ad_data['adid'] == adid].iloc[0]
        country = ad_info['country']

        # 当前轮次流量占比
        round_end_time = current_time + timedelta(minutes=self.round_minutes)
        current_round_traffic_share = self.traffic_allocator.get_traffic_share(
            country, current_time, round_end_time, end_time
        )

        round_budget = total_budget * current_round_traffic_share
        return round_budget

    def simulate_auction(self, bid: float, bid_price: float) -> bool:
        """模拟一价拍卖, bid_price单位为 美分/1k次"""
        return bid >= bid_price

    def run_simulation(self):
        """运行离线模拟"""
        logger.info("begin to run simulation...")
        self.prepare_data()

        adids = self.ad_data['adid'].unique()

        # 初始化每个ad的状态
        ad_states = {}
        for adid in adids:
            ad_info = self.ad_data[self.ad_data['adid'] == adid].iloc[0]
            ad_states[adid] = {
                'remaining_budget': ad_info['budget'],
                'total_cost': 0.0,
                'total_cv': 0,
                'round_cost': 0.0,
                'round_cv': 0,
                'ecpa_phi': 0.0,
                'budget_phi': 0.0,
                'ecpa': ad_info['ecpa'],
                'round_bids': [],  # 记录每轮出价
                'round_limited_bids': 0,  # 记录每轮被A_upper限制的出价次数
                'budget_reference': 0.0,  # 记录每轮预算参考值
                'ecpc_reference': ad_info['target_cpa'],  # 记录每轮ECPC参考值（即目标CPA）
                'pid_details': {}  # 记录PID控制器详细信息
            }

        start_time = self.impression_data['time'].min()
        logger.info(f"start time: {start_time}")
        end_time = start_time + timedelta(hours=24)  # 模拟一天
        current_time = start_time
        round_num = 0

        logger.info(f"begin to initialize campaign traffic...")
        for adid in adids:
            ad_info = self.ad_data[self.ad_data['adid'] == adid].iloc[0]
            self.controller.initialize_campaign_traffic(adid, ad_info['budget'])

        while current_time < end_time:
            round_end_time = current_time + timedelta(minutes=self.round_minutes)
            logger.info(f"begin to run round {round_num + 1} simulation ({current_time.strftime('%H:%M')} - {round_end_time.strftime('%H:%M')})")

            # 获取当前轮次的曝光数据
            round_impressions = self.impression_data[
                (self.impression_data['time'] >= current_time) &
                (self.impression_data['time'] < round_end_time)
                ]

            # 为每个adid更新phi参数
            logger.info(f"begin to update phi parameters...")
            for adid in adids:
                ad_states[adid]['ecpa_phi'] = 0.0
                ad_states[adid]['budget_phi'] = 0.0

                if ad_states[adid]['remaining_budget'] <= 0:
                    continue

                ad_info = self.ad_data[self.ad_data['adid'] == adid].iloc[0]
                target_cpa = ad_info['target_cpa']

                # 分配当前轮次预算
                round_budget = self.allocate_budget(
                    adid, ad_states[adid]['remaining_budget'], current_time, end_time
                )

                # 保存参考值
                ad_states[adid]['ecpc_reference'] = target_cpa

                # 获取当前累计流量份额（从活动开始到当前时间）
                current_traffic_share = self.traffic_allocator.get_traffic_share(
                    ad_info['country'], start_time, current_time, end_time
                )
                target_spend = self.controller.avg_spend[adid]
                ad_states[adid]['budget_reference'] = target_spend

                # 更新phi参数
                ecpa_phi, budget_phi, pid_details = self.controller.update_phi(
                    adid, target_cpa, target_spend,
                    ad_states[adid]['round_cost'], ad_states[adid]['round_cv'],
                    current_traffic_share
                )
                ad_states[adid]['ecpa_phi'] = ecpa_phi
                ad_states[adid]['budget_phi'] = budget_phi
                ad_states[adid]['pid_details'] = pid_details

            # 统计每个adid在当前轮次的曝光数量
            round_impression_counts = round_impressions.groupby('adid').size().to_dict() if len(round_impressions) > 0 else {}

            # 重置轮次计数器
            for adid in adids:
                ad_states[adid]['round_cost'] = 0.0
                ad_states[adid]['round_cv'] = 0
                ad_states[adid]['round_bids'] = []
                ad_states[adid]['round_limited_bids'] = 0
                ad_states[adid]['round_impressions'] = round_impression_counts.get(adid, 0)

            logger.info(f"begin to process round impressions, impressions count: {len(round_impressions)}...")
            # 处理当前轮次的每个曝光
            for _, impression in round_impressions.iterrows():
                adid = impression['adid']

                if ad_states[adid]['remaining_budget'] <= 0:
                    continue

                # 计算出价
                bid, is_limited = self.controller.compute_bid(
                    impression['ctr'], impression['cvr'],
                    ad_states[adid]['ecpa'], impression['target_cpa'],
                    ad_states[adid]['ecpa_phi'], ad_states[adid]['budget_phi']
                )

                ad_states[adid]['round_bids'].append(bid)
                if is_limited:
                    ad_states[adid]['round_limited_bids'] += 1

                if self.simulate_auction(bid, impression['bid_price']):
                    cost = bid / 1000 / 100 # 一价拍卖，支付出价
                    cv = impression['cv']

                    if ad_states[adid]['remaining_budget'] >= cost:
                        ad_states[adid]['remaining_budget'] = round(ad_states[adid]['remaining_budget'] - cost, 3)
                        ad_states[adid]['total_cost'] = round(ad_states[adid]['total_cost'] + cost, 3)
                        ad_states[adid]['round_cost'] = round(ad_states[adid]['round_cost'] + cost, 3)
                        if cv:
                            ad_states[adid]['total_cv'] += 1
                            ad_states[adid]['round_cv'] += 1

                        self.results.append({
                            'time': impression['time'],
                            'adid': adid,
                            'bid': bid,
                            'bid_price': impression['bid_price'],
                            'cost': cost,
                            'cv': cv,
                            'p_cv': impression['ctr'] * impression['cvr'],
                            'ecpa_phi': ad_states[adid]['ecpa_phi'],
                            'budget_phi': ad_states[adid]['budget_phi'],
                            'round': round_num,
                            'is_limited_by_upper': is_limited
                        })

            # 记录轮次结果
            for adid in adids:
                current_ecpa = ad_states[adid]['total_cost'] / max(ad_states[adid]['total_cv'], 1)
                round_bids = ad_states[adid]['round_bids']
                avg_bid = sum(round_bids) / len(round_bids) if round_bids else 0

                round_result = {
                    'round': round_num,
                    'adid': adid,
                    'total_cost': float(f'{ad_states[adid]["total_cost"]:.3f}'),
                    'total_cv': ad_states[adid]["total_cv"],
                    'current_ecpa': float(f'{current_ecpa:.3f}'),
                    'remaining_budget': float(f'{ad_states[adid]["remaining_budget"]:.3f}'),
                    'ecpa_phi': float(f'{ad_states[adid]["ecpa_phi"]:.6f}'),
                    'budget_phi': float(f'{ad_states[adid]["budget_phi"]:.6f}'),
                    'round_cost': float(f'{ad_states[adid]["round_cost"]:.3f}'),
                    'round_cv': ad_states[adid]["round_cv"],
                    'round_impressions': ad_states[adid]["round_impressions"],
                    'avg_bid': float(f'{avg_bid:.3f}'),
                    'limited_bids_ratio': float(f'{ad_states[adid]["round_limited_bids"] / len(round_bids):.3f}') if round_bids else 0.0,
                    'budget_reference': float(f'{ad_states[adid]["budget_reference"]:.3f}'),
                    'ecpc_reference': float(f'{ad_states[adid]["ecpc_reference"]:.3f}'),
                    'pid_details': json.dumps(ad_states[adid].get('pid_details', {}))
                }

                self.round_results.append(round_result)
            current_time = round_end_time
            round_num += 1

        logger.info(f"simulation done, total {round_num} rounds")

    def cal_settling_time(self, ecpcs: Dict[int, float], ref: float, settle_con: float = 0.1) -> int:
        """计算调节时间"""
        settled = False
        settling_time = 0
        cntr_rounds = len(ecpcs)

        for round_num in sorted(ecpcs.keys()):
            value = ecpcs[round_num]
            error = ref - value
            if abs(error) / ref <= settle_con and not settled:
                settled = True
                settling_time = round_num
            elif abs(error) / ref > settle_con:
                settled = False
                settling_time = cntr_rounds
        return settling_time

    def cal_rmse_ss(self, ecpcs: Dict[int, float], ref: float, settle_con: float = 0.1) -> float:
        """计算稳态均方根误差（Steady-State RMSE）"""
        settling_time = self.cal_settling_time(ecpcs, ref, settle_con)
        cntr_rounds = len(ecpcs)

        if settling_time >= cntr_rounds:
            settling_time = cntr_rounds - 1

        rmse = 0.0
        count = 0
        for round_num in sorted(ecpcs.keys()):
            if round_num >= settling_time:
                rmse += (ecpcs[round_num] - ref) * (ecpcs[round_num] - ref)
                count += 1

        if count > 0:
            rmse /= count
            rmse = math.sqrt(rmse) / ref  # 相对RMSE

        return rmse

    def cal_rise_time(self, ecpcs: Dict[int, float], ref: float, rise_con: float = 0.9) -> int:
        """计算上升时间"""
        rise_time = 0
        for round_num in sorted(ecpcs.keys()):
            value = ecpcs[round_num]
            error = ref - value
            if abs(error) / ref <= (1 - rise_con):
                rise_time = round_num
                break
        return rise_time

    def cal_overshoot(self, ecpcs: Dict[int, float], ref: float) -> float:
        """计算超调量"""
        if not ecpcs:
            return 0.0

        first_round = min(ecpcs.keys())
        first_value = ecpcs[first_round]

        if first_value > ref:
            min_val = first_value
            for value in ecpcs.values():
                if value <= min_val:
                    min_val = value
            if min_val < ref:
                return (ref - min_val) * 100.0 / ref
            else:
                return 0.0
        elif first_value < ref:
            max_val = first_value
            for value in ecpcs.values():
                if value >= max_val:
                    max_val = value
            if max_val > ref:
                return (max_val - ref) * 100.0 / ref
            else:
                return 0.0
        else:
            max_deviation = 0
            for value in ecpcs.values():
                if abs(value - ref) >= max_deviation:
                    max_deviation = abs(value - ref)
            return max_deviation * 100.0 / ref

    def calculate_metrics(self) -> Dict[str, Any]:
        """计算评估指标"""
        if not self.results:
            return {}

        results_df = pd.DataFrame(self.results)
        round_results_df = pd.DataFrame(self.round_results)

        metrics = {}

        # 整体指标
        total_cost = results_df['cost'].sum()
        total_cv = results_df['cv'].sum()
        total_p_cv = results_df['p_cv'].sum()
        overall_ecpa = total_cost / total_cv if total_cv > 0 else 0.0

        metrics['overall'] = {
            'total_cost': total_cost,
            'total_cv': total_cv,
            'total_p_cv': total_p_cv,
            'overall_ecpa': overall_ecpa,
            'total_impressions': len(results_df),
            'avg_bid': results_df['bid'].mean()
        }

        # 按adid的指标
        metrics['by_adid'] = {}
        for adid in self.ad_data['adid'].unique():
            adid_results = results_df[results_df['adid'] == adid]
            adid_round_results = round_results_df[round_results_df['adid'] == adid]
            ad_info = self.ad_data[self.ad_data['adid'] == adid].iloc[0]

            if len(adid_results) > 0:
                adid_cost = adid_results['cost'].sum()
                adid_cv = adid_results['cv'].sum()
                adid_p_cv = adid_results['p_cv'].sum()
                adid_ecpa = adid_cost / max(adid_cv, 1)
                budget_utilization = adid_cost / ad_info['budget']

                ecpcs = {}
                for _, row in adid_round_results.iterrows():
                    ecpcs[row['round']] = row['current_ecpa']

                target_cpa = ad_info['target_cpa']

                # 计算各项控制指标
                settling_time = self.cal_settling_time(ecpcs, target_cpa) if ecpcs else 0
                rmse_ss = self.cal_rmse_ss(ecpcs, target_cpa) if ecpcs else 0.0
                rise_time = self.cal_rise_time(ecpcs, target_cpa) if ecpcs else 0
                overshoot = self.cal_overshoot(ecpcs, target_cpa) if ecpcs else 0.0

                metrics['by_adid'][adid] = {
                    'cost': adid_cost,
                    'cv': adid_cv,
                    'p_cv': adid_p_cv,
                    'ecpa': adid_ecpa,
                    'target_cpa': ad_info['target_cpa'],
                    'budget_utilization': budget_utilization,
                    'cpa_achievement': adid_ecpa / ad_info['target_cpa'] if ad_info['target_cpa'] > 0 else float('inf'),
                    'p_cv_ratio': adid_p_cv / total_p_cv if total_p_cv > 0 else 0.0,
                    'settling_time': settling_time,
                    'rmse_ss': rmse_ss,
                    'rise_time': rise_time,
                    'overshoot': overshoot
                }

        return metrics

    def plot_results(self, target_adid_list: List[str] = None):
        """绘制结果图表"""
        if not self.round_results:
            logger.warning("no round results data, skip plot")
            return
        if target_adid_list is None:
            logger.warning("target_adid_list is None, skip plot")
            return

        round_results_df = pd.DataFrame(self.round_results)

        # 创建子图 - 修改为4x2布局以容纳新的cost图表
        fig, axes = plt.subplots(4, 2, figsize=(15, 20))
        fig.suptitle('CPA and budget control simulation results', fontsize=16)

        # 1. eCPA变化趋势
        ax1 = axes[0, 0]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            target_cpa = self.ad_data[self.ad_data['adid'] == adid].iloc[0]['target_cpa']

            ax1.plot(adid_data['round'], adid_data['current_ecpa'],
                     label=f'AdID {adid}', marker='o', markersize=3)
            ax1.axhline(y=target_cpa, color='red', linestyle='--', alpha=0.5)

        ax1.set_title('eCPA trend')
        ax1.set_xlabel('round')
        ax1.set_ylabel('eCPA')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 预算使用情况
        ax2 = axes[0, 1]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            total_budget = self.ad_data[self.ad_data['adid'] == adid].iloc[0]['budget']
            budget_used = total_budget - adid_data['remaining_budget']
            budget_utilization = budget_used / total_budget * 100

            ax2.plot(adid_data['round'], budget_utilization,
                     label=f'AdID {adid}', marker='s', markersize=3)

        ax2.set_title('budget utilization trend')
        ax2.set_xlabel('round')
        ax2.set_ylabel('budget utilization (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. Phi参数变化
        ax3 = axes[1, 0]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            ax3.plot(adid_data['round'], adid_data['ecpa_phi'],
                     label=f'AdID {adid} (eCPA)', marker='^', markersize=3, linestyle='-')
            ax3.plot(adid_data['round'], adid_data['budget_phi'],
                     label=f'AdID {adid} (Budget)', marker='v', markersize=3, linestyle='--')

        ax3.set_title('Phi params trend')
        ax3.set_xlabel('round')
        ax3.set_ylabel('Phi value')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 每轮转化数变化
        ax4 = axes[1, 1]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            ax4.plot(adid_data['round'], adid_data['round_cv'],
                     label=f'AdID {adid}', marker='d', markersize=3)

        ax4.set_title('round cv trend')
        ax4.set_xlabel('round')
        ax4.set_ylabel('cv')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 平均出价变化趋势
        ax5 = axes[2, 0]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            ax5.plot(adid_data['round'], adid_data['avg_bid'],
                     label=f'AdID {adid}', marker='*', markersize=3)

        ax5.set_title('average bid trend')
        ax5.set_xlabel('round')
        ax5.set_ylabel('average bid')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 出价限制比例变化趋势
        ax6 = axes[2, 1]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            ax6.plot(adid_data['round'], adid_data['limited_bids_ratio'] * 100,
                     label=f'AdID {adid}', marker='x', markersize=3)

        ax6.set_title('bid limitation ratio trend')
        ax6.set_xlabel('round')
        ax6.set_ylabel('limited bids ratio (%)')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        # 7. 每轮cost变化趋势 - 新增图表
        ax7 = axes[3, 0]
        for adid in target_adid_list:
            adid_data = round_results_df[round_results_df['adid'] == adid]
            ax7.plot(adid_data['round'], adid_data['round_cost'],
                     label=f'AdID {adid}', marker='o', markersize=3)

        ax7.set_title('round cost trend')
        ax7.set_xlabel('round')
        ax7.set_ylabel('round cost')
        ax7.legend()
        ax7.grid(True, alpha=0.3)


        plt.tight_layout()
        plt.show()

        # 保存图片
        plt.savefig(f'data/predictions/oversea_dsp/fb_control/fb_control_m/simulation_results.png', dpi=300, bbox_inches='tight')
        logger.info(f"results plot saved to data/predictions/oversea_dsp/fb_control/fb_control_m/simulation_results.png")


class ParameterOptimizer:
    """参数优化器，用于探索最优的PID控制器参数"""

    def __init__(self, impression_data: pd.DataFrame, ad_data: pd.DataFrame,
                 traffic_data: pd.DataFrame, metric: str = 'ecpa'):
        self.impression_data = impression_data
        self.ad_data = ad_data
        self.traffic_data = traffic_data
        self.metric = metric
        self.best_metrics = {
            'ecpa': float('inf'),
            'budget_utilization': 0.0,
            'cpa_achievement': 0.0,
            'total_cv': 0,
            'settling_time': float('inf'),
            'rmse_ss': float('inf'),
            'rise_time': float('inf'),
            'overshoot': float('inf')
        }

    def objective(self, trial: optuna.Trial) -> float:
        """优化目标函数"""
        params = {
            'round_minutes': ROUND_MINUTES,
            'min_bid': MIN_BID,
            'pid_params': {
                'ecpa': {
                    'kp': trial.suggest_float('ecpa_kp', 1e-4, 1, log=True),
                    'ki': trial.suggest_float('ecpa_ki', 1e-6, 1e-1, log=True),
                    'kd': trial.suggest_float('ecpa_kd', 1e-6, 1e-1, log=True)
                },
                'budget': {
                    'kp': trial.suggest_float('budget_kp', 1e-4, 1, log=True),
                    'ki': trial.suggest_float('budget_ki', 1e-6, 1e-1, log=True),
                    'kd': trial.suggest_float('budget_kd', 1e-6, 1e-1, log=True)
                }
            }
        }

        simulator = OfflineSimulator(
            self.impression_data.copy(),
            self.ad_data.copy(),
            self.traffic_data.copy(),
            params
        )
        simulator.run_simulation()

        # 计算评估指标
        metrics = simulator.calculate_metrics()

        if metrics == {}:
            return float('inf')

        # 收集控制系统性能指标
        settling_times = []
        rmse_ss_values = []
        rise_times = []
        overshoots = []

        for adid, adid_metrics in metrics['by_adid'].items():
            # 收集控制系统性能指标
            settling_times.append(adid_metrics['settling_time'])
            rmse_ss_values.append(adid_metrics['rmse_ss'])
            rise_times.append(adid_metrics['rise_time'])
            overshoots.append(adid_metrics['overshoot'])

        # 计算控制系统性能指标的平均值
        avg_settling_time = np.mean(settling_times) if settling_times else float('inf')
        avg_rmse_ss = np.mean(rmse_ss_values) if rmse_ss_values else float('inf')
        avg_rise_time = np.mean(rise_times) if rise_times else float('inf')
        avg_overshoot = np.mean(overshoots) if overshoots else float('inf')

        # 更新最佳指标
        if avg_settling_time < self.best_metrics['settling_time']:
            self.best_metrics['settling_time'] = avg_settling_time
        if avg_rmse_ss < self.best_metrics['rmse_ss']:
            self.best_metrics['rmse_ss'] = avg_rmse_ss
        if avg_rise_time < self.best_metrics['rise_time']:
            self.best_metrics['rise_time'] = avg_rise_time
        if avg_overshoot < self.best_metrics['overshoot']:
            self.best_metrics['overshoot'] = avg_overshoot

        # 根据优化目标返回相应的指标
        if self.metric == 'settling_time':
            return avg_settling_time
        elif self.metric == 'rmse_ss':
            return avg_rmse_ss
        elif self.metric == 'rise_time':
            return avg_rise_time
        elif self.metric == 'overshoot':
            return avg_overshoot
        else:
            raise ValueError(f"Unknown metric: {self.metric}")

    def optimize(self, n_trials: int = 100, metric: str = 'ecpa') -> optuna.Study:
        """运行参数优化"""
        self.metric = metric
        # 定义需要最小化的指标
        minimize_metrics = ['settling_time', 'rmse_ss', 'rise_time', 'overshoot']
        direction = 'minimize' if metric in minimize_metrics else 'maximize'

        study = optuna.create_study(direction=direction)
        study.optimize(self.objective, n_trials=n_trials, n_jobs=6)

        logger.info('Best trial:')
        trial = study.best_trial
        logger.info(f'  Value: {trial.value}')
        logger.info('  Params: ')

        # 保存最佳参数
        params_dict = dict()
        for key, value in trial.params.items():
            logger.info(f'    {key}: {value}')
            params_dict[key] = value

        base_dir = 'data/models/oversea_dsp/fb_control/fb_control_m'
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        dict_path = f'{base_dir}/pid_{metric}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
        with open(dict_path, 'wb') as f:
            pickle.dump(params_dict, f)

        logger.info(f"\nBest metrics achieved:")
        logger.info(f"eCPA: {self.best_metrics['ecpa']:.3f}")
        logger.info(f"Budget utilization: {self.best_metrics['budget_utilization']:.2%}")
        logger.info(f"CPA achievement: {self.best_metrics['cpa_achievement']:.2f}")
        logger.info(f"=== 控制系统性能指标 ===")
        logger.info(f"调节时间: {self.best_metrics['settling_time']:.1f} 轮")
        logger.info(f"稳态RMSE: {self.best_metrics['rmse_ss']:.4f}")
        logger.info(f"上升时间: {self.best_metrics['rise_time']:.1f} 轮")
        logger.info(f"超调量: {self.best_metrics['overshoot']:.2f}%")
        logger.info(f"优化目标 {metric}: {trial.value:.4f}")

        return study


def map_impression_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    映射曝光数据的字段名称

    原始字段: bid_id, calibrated_pctr, calibrated_pcvr, impress_time, adid, bid_price, cv
    映射后字段: bid_id, ctr, cvr, time, adid, bid_price, cv, p_cv
    """
    mapped_df = pd.DataFrame()

    mapped_df['bid_id'] = df['bid_id']
    mapped_df['adid'] = df['adid']
    mapped_df['bid_price'] = df['bid_price']
    mapped_df['cv'] = df['cv']

    mapped_df['ctr'] = df['calibrated_pctr']
    mapped_df['cvr'] = df['calibrated_pcvr']
    mapped_df['time'] = pd.to_datetime(df['impress_time'])

    mapped_df['p_cv'] = mapped_df['ctr'] * mapped_df['cvr']

    logger.info(f"impression data mapping done: {len(df)} records")

    return mapped_df


def map_traffic_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    映射流量数据的字段名称

    原始字段: country, dt, hours, minutes, request_cnt
    映射后字段: country, datetime, pv
    """
    mapped_df = pd.DataFrame()

    mapped_df['country'] = df['country']
    mapped_df['pv'] = pd.to_numeric(df['request_cnt'], errors='coerce')

    try:
        hours = pd.to_numeric(df['hours'], errors='coerce').fillna(0).astype(int)
        minutes = pd.to_numeric(df['minutes'], errors='coerce').fillna(0).astype(int)

        base_date = pd.to_datetime(df['dt'])
        beijing_datetime = base_date + pd.to_timedelta(hours, unit='h') + pd.to_timedelta(minutes, unit='m')

        # 将北京时间（UTC+8）转换为UTC时间（UTC+0）
        # 北京时间比UTC时间快8小时，所以需要减去8小时
        mapped_df['datetime'] = beijing_datetime - pd.Timedelta(hours=8)
    except Exception as e:
        raise ValueError(f"time field combination or timezone conversion failed: {e}")

    logger.info(f"traffic data mapping done: {len(df)} records")
    return mapped_df


def load_dataset(impression_file: str, ad_info_file: str, traffic_file: str, dataset_name: str = "dataset") -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    加载单个数据集（训练集或测试集）

    Args:
        impression_file: 曝光数据文件路径 (CSV格式)
        ad_info_file: 广告信息数据文件路径 (CSV格式)
        traffic_file: 流量数据文件路径 (CSV格式)
        dataset_name: 数据集名称，用于日志输出

    Returns:
        impression_df: 曝光数据DataFrame
        ad_data: 广告信息DataFrame
        traffic_df: 流量数据DataFrame

    数据格式要求:
    1. 曝光数据必须包含字段: bid_id, adid, ctr, cvr, bid_price(单位: 美分/1k曝光), cv, p_cv, time
    2. 广告信息数据必须包含字段: adid, ecpa(单位: 美元/cv), target_cpa(单位: 美元/cv), budget(单位: 美元), country
    3. 流量数据必须包含字段: country, datetime, pv
    """
    logger.info(f"begin to load {dataset_name} data...")

    try:
        # 加载曝光数据
        logger.info(f"load {dataset_name} impression data: {impression_file}")
        impression_df = pd.read_csv(impression_file, sep='\t')
        impression_df = map_impression_columns(impression_df)

        # 加载广告信息数据
        logger.info(f"load {dataset_name} ad info data: {ad_info_file}")
        ad_data = pd.read_csv(ad_info_file, sep='\t')
        ad_data = ad_data[(ad_data['ecpa'] != '""') & (ad_data['ecpa'].notna())]
        ad_data['ecpa'] = pd.to_numeric(ad_data['ecpa'], errors='coerce')
        ad_data['target_cpa'] = pd.to_numeric(ad_data['target_cpa'], errors='coerce')
        ad_data['budget'] = pd.to_numeric(ad_data['budget'], errors='coerce')
        ad_data['country'] = ad_data['country'].str.split(',').str[0]

        # 加载流量数据
        logger.info(f"begin to load {dataset_name} traffic data: {traffic_file}")
        traffic_df = pd.read_csv(traffic_file, sep='\t')
        traffic_df = map_traffic_columns(traffic_df)

        # 移除有缺失值的行
        impression_df.dropna(inplace=True)

        # 确保按时间排序
        impression_df.sort_values('time', inplace=True)
        impression_df.reset_index(drop=True, inplace=True)

        traffic_df.sort_values('datetime', inplace=True)
        traffic_df.reset_index(drop=True, inplace=True)

        # 输出数据概览
        logger.info(f"{dataset_name} data loaded done!")
        logger.info(f"  impression data: {len(impression_df)} records, total cv: {impression_df['cv'].sum()}")
        top5_cv_adids = impression_df.groupby('adid')['cv'].sum().nlargest(5)
        logger.info(f"  top 5 cv adids: {dict(top5_cv_adids)}")
        logger.info(f"  ad data: {len(ad_data)} records")
        logger.info(f"  traffic data: {len(traffic_df)} records")

        return impression_df, ad_data, traffic_df
    except Exception as e:
        logger.error(f"error: load {dataset_name} data failed - {traceback.format_exc()}")
        raise


def evaluate_simulator(simulator: OfflineSimulator, dataset_name: str):
    """评估模拟器结果"""
    metrics = simulator.calculate_metrics()

    # 计算总预算
    total_budget = simulator.ad_data['budget'].sum()
    total_cv = simulator.impression_data['cv'].sum()
    total_p_cv = simulator.impression_data['p_cv'].sum()

    # 创建评估结果文件
    evaluation_dir = f'data/predictions/oversea_dsp/fb_control/fb_control_m'
    result_file = f'{evaluation_dir}/evaluation_{dataset_name}_results.txt'
    if not os.path.exists(evaluation_dir):
        os.makedirs(evaluation_dir)

    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"=== {dataset_name} 模拟结果 ===\n")
        f.write(f"总消耗: {metrics['overall']['total_cost']:.2f}\n")
        f.write(f"总预算: {total_budget:.2f}\n")
        f.write(f"总预算使用率: {metrics['overall']['total_cost']/total_budget:.2%}\n")
        f.write(f"总转化: {metrics['overall']['total_cv']}\n")
        f.write(f"总转化Ratio: {metrics['overall']['total_cv']/total_cv:.2%}\n")
        f.write(f"总预测转化: {metrics['overall']['total_p_cv']:.2f}\n")
        f.write(f"总预测转化Ratio: {metrics['overall']['total_p_cv']/total_p_cv:.2%}\n")
        f.write(f"整体eCPA: {metrics['overall']['overall_ecpa']:.2f}\n")
        f.write(f"总曝光: {metrics['overall']['total_impressions']}\n")
        f.write(f"平均出价: {metrics['overall']['avg_bid']:.3f}\n\n")

        f.write(f"=== {dataset_name} 各广告表现 ===\n")
        for adid, adid_metrics in metrics['by_adid'].items():
            cost_ratio = adid_metrics['cost'] / metrics['overall']['total_cost'] if metrics['overall']['total_cost'] > 0 else 0
            cv_ratio = adid_metrics['cv'] / metrics['overall']['total_cv'] if metrics['overall']['total_cv'] > 0 else 0

            f.write(f"AdID {adid}:\n")
            f.write(f"  消耗: {adid_metrics['cost']:.2f} (占总消耗 {cost_ratio:.1%})\n")
            f.write(f"  转化: {adid_metrics['cv']} (占总转化 {cv_ratio:.1%})\n")
            f.write(f"  预测转化: {adid_metrics['p_cv']:.2f} (占总预测转化 {adid_metrics['p_cv_ratio']:.1%})\n")
            f.write(f"  eCPA: {adid_metrics['ecpa']:.2f}\n")
            f.write(f"  目标CPA: {adid_metrics['target_cpa']:.2f}\n")
            f.write(f"  CPA达成率: {adid_metrics['cpa_achievement']:.2f}\n")
            f.write(f"  预算使用率: {adid_metrics['budget_utilization']:.2%}\n")
            f.write(f"  === 控制系统性能指标 ===\n")
            f.write(f"  调节时间: {adid_metrics['settling_time']} 轮\n")
            f.write(f"  上升时间: {adid_metrics['rise_time']} 轮\n")
            f.write(f"  稳态RMSE: {adid_metrics['rmse_ss']:.4f}\n")
            f.write(f"  超调量: {adid_metrics['overshoot']:.2f}%\n\n")

    # 在日志中只输出简要汇总信息
    logger.info(f"\n=== {dataset_name} 模拟结果汇总 ===")
    logger.info(f"总消耗: {metrics['overall']['total_cost']:.2f} / 总预算: {total_budget:.2f} (使用率: {metrics['overall']['total_cost']/total_budget:.2%})")
    logger.info(f"总转化: {metrics['overall']['total_cv']} / 整体eCPA: {metrics['overall']['overall_ecpa']:.2f}")
    logger.info(f"广告数量: {len(metrics['by_adid'])} 个")
    logger.info(f"详细评估结果已保存到: {result_file}")

    # 保存详细结果
    results_df = pd.DataFrame(simulator.results)
    round_results_df = pd.DataFrame(simulator.round_results)

    results_df.to_csv(f'{evaluation_dir}/simulation_{dataset_name}_detailed_results.csv', index=False)
    round_results_df.to_csv(f'{evaluation_dir}/simulation_{dataset_name}_round_results.csv', index=False)

    # 绘制结果
    simulator.plot_results()
    plt.savefig(f'data/predictions/oversea_dsp/fb_control/fb_control_m/simulation_{dataset_name}_results.png', dpi=300, bbox_inches='tight')

    return metrics


def load_best_params(param_file: str = None) -> Dict[str, Any]:
    """
    从pkl文件加载最佳参数

    Args:
        param_file: 参数文件路径，如果为None则使用默认参数

    Returns:
        controller_params: 控制器参数字典
    """

    if param_file is None or not os.path.exists(param_file):
        logger.info("params file not specified or not exist, return None")
        return None

    try:
        logger.info(f"load best params file: {param_file}")
        with open(param_file, 'rb') as f:
            params_dict = pickle.load(f)

        controller_params = {
            'round_minutes': ROUND_MINUTES,
            'min_bid': MIN_BID,
            'pid_params': {
                'ecpa': {
                    'kp': params_dict['ecpa_kp'],
                    'ki': params_dict['ecpa_ki'],
                    'kd': params_dict['ecpa_kd']
                },
                'budget': {
                    'kp': params_dict['budget_kp'],
                    'ki': params_dict['budget_ki'],
                    'kd': params_dict['budget_kd']
                }
            }
        }

        logger.info(f"  eCPA PID: kp={params_dict['ecpa_kp']:.6f}, ki={params_dict['ecpa_ki']:.6f}, kd={params_dict['ecpa_kd']:.6f}")
        logger.info(f"  Budget PID: kp={params_dict['budget_kp']:.6f}, ki={params_dict['budget_ki']:.6f}, kd={params_dict['budget_kd']:.6f}")

        return controller_params

    except Exception as e:
        logger.error(f"load best params file failed: {e}")
        return None


def main():
    """主函数"""

    try:
        study_flag = False

        base_dir = 'data/training_data/oversea_dsp/fb_control/fb_control_m'
        train_impression_file = f"{base_dir}/2025-06-15/imp.csv"
        test_impression_file = f"{base_dir}/2025-06-16/imp.csv"
        train_ad_info_file = f"{base_dir}/2025-06-15/ad_info.txt"  # 训练集广告信息文件
        test_ad_info_file = f"{base_dir}/2025-06-16/ad_info.txt"   # 测试集广告信息文件
        train_traffic_file = f"{base_dir}/2025-06-15/traffic.txt"
        test_traffic_file = f"{base_dir}/2025-06-16/traffic.txt"

        # 分别加载训练集和测试集数据
        train_impressions, train_ad_data, train_traffic = load_dataset(
            train_impression_file, train_ad_info_file, train_traffic_file, "train_dataset"
        )
        test_impressions, test_ad_data, test_traffic = load_dataset(
            test_impression_file, test_ad_info_file, test_traffic_file, "test_dataset"
        )


        if study_flag:

            # 在训练集上创建参数优化器
            optimizer = ParameterOptimizer(train_impressions, train_ad_data, train_traffic)

            # 在训练集上运行参数优化
            logger.info("begin to optimize parameters on train dataset...")
            study = optimizer.optimize(n_trials=50, metric='rmse_ss')

            # 获取最佳参数
            best_params = study.best_trial.params
            controller_params = {
                'round_minutes': ROUND_MINUTES,
                'min_bid': MIN_BID,
                'pid_params': {
                    'ecpa': {
                        'kp': best_params['ecpa_kp'],
                        'ki': best_params['ecpa_ki'],
                        'kd': best_params['ecpa_kd']
                    },
                    'budget': {
                        'kp': best_params['budget_kp'],
                        'ki': best_params['budget_ki'],
                        'kd': best_params['budget_kd']
                    }
                }
            }
        else:
            # 从pkl文件加载最佳参数
            #best_params_file = 'data/models/oversea_dsp/fb_control/fb_control_m/pid_rmse_ss_20250623_070701.pkl'
            best_params_file = 'data/models/oversea_dsp/fb_control/fb_control_m/pid_rmse_ss_20250624_032614.pkl'
            logger.info(f"load best params from pkl file: {best_params_file}...")
            controller_params = load_best_params(best_params_file)
            logger.info(f"best params: {controller_params}")

        # 在训练集上运行模拟
        logger.info("begin to run simulation on train dataset...")
        train_simulator = OfflineSimulator(train_impressions, train_ad_data, train_traffic, controller_params)
        train_simulator.run_simulation()
        train_metrics = evaluate_simulator(train_simulator, "train")

        # 在测试集上运行模拟
        logger.info("begin to run simulation on test dataset...")
        test_simulator = OfflineSimulator(test_impressions, test_ad_data, test_traffic, controller_params)
        test_simulator.run_simulation()
        test_metrics = evaluate_simulator(test_simulator, "test")

        # 比较训练集和测试集的结果
        logger.info("\n=== 训练集和测试集结果对比 ===")
        logger.info(f"训练集 eCPA: {train_metrics['overall']['overall_ecpa']:.2f}")
        logger.info(f"测试集 eCPA: {test_metrics['overall']['overall_ecpa']:.2f}")
        logger.info(f"eCPA 差异: {abs(train_metrics['overall']['overall_ecpa'] - test_metrics['overall']['overall_ecpa']):.2f}")

        logger.info("\n详细结果已保存:")
        logger.info("- simulation_train_detailed_results.csv: 训练集详细曝光级别结果")
        logger.info("- simulation_train_round_results.csv: 训练集轮次级别结果")
        logger.info("- simulation_train_results.png: 训练集结果可视化图表")
        logger.info("- simulation_test_detailed_results.csv: 测试集详细曝光级别结果")
        logger.info("- simulation_test_round_results.csv: 测试集轮次级别结果")
        logger.info("- simulation_test_results.png: 测试集结果可视化图表")
    except Exception as e:
        logger.error(f"fb control error: {traceback.format_exc()}")


if __name__ == "__main__":
    main()