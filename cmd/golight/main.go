package main

import (
	"fmt"
	"log"
	"os"

	gl "gitlab.ydmob.com/algorithm/golightly"
)

//LIGHTGBM_C_EXPORT int LGBM_BoosterPredictForMatSingleRowFastInit(
//BoosterHandle handle,
//const int predict_type,
//const int start_iteration,
//const int num_iteration,
//const int data_type,
//const int32_t ncol,
//const char* parameter,
//FastConfigHandle *out_fastConfig
//);

//LIGHTGBM_C_EXPORT int LGBM_BoosterPredictForMat(
//BoosterHandle handle,       // 训练好的 LightGBM 模型
//const void* data,           // 输入数据
//int data_type,              // 输入数据类型
//int32_t nrow,               // 行数（样本数量）
//int32_t ncol,               // 列数（特征数量）
//int is_row_major,           // 数据存储格式（行优先 or 列优先）
//int predict_type,           // 预测类型
//int start_iteration,        // 预测的起始迭代次数
//int num_iteration,          // 预测时使用的迭代次数
//const char* parameter,      // 额外的参数
//int64_t* out_len,           // 预测结果的长度
//double* out_result          // 预测结果
//);

// ./winequality_red_train.tsv ./winequality_red_valid.tsv
func test() {
	if len(os.Args) != 3 {
		log.Fatal("Please include files for training and validation")
	}
	trainFile := os.Args[1]
	validFile := os.Args[2]

	model := gl.CreateSimpleRegressionModel(trainFile, validFile)
	var numModels int32
	cErr := gl.BoosterNumberOfTotalModel(model, &numModels)
	if cErr != 0 {
		log.Fatal("Error getting number of trees in model")
	}
	cErr = gl.BoosterSaveModel(model, 0, numModels, gl.Importance_gain, "./trained_model.txt")
	if cErr != 0 {
		log.Fatal("Error saving model")
	}
}

func main1() {
	gl.InitializeLightgbm("./lib_lightgbm.so")
	var cErr int
	modelFile := "./trained_model.txt"
	iter := int32(0)
	var booster gl.Booster
	cErr = gl.BoosterCreateFromModelfile(modelFile, &iter, &booster)
	if cErr != 0 {
		log.Fatal(cErr)
	}

	// configure fast prediction
	data := []float32{8.4, 0.37, 0.43, 2.3, 0.063, 12, 19, 0.9955, 3.17, 0.81, 11.2}
	dataType := int32(0)
	nCols := int32(len(data))
	startIter := int32(0)
	numIter := int32(64)
	parameter := ""
	var fastConfig gl.FastConfig
	cErr = gl.BoosterPredictForMatSingleRowFastInit(booster, gl.PredictNormal, startIter, numIter, dataType, nCols, parameter, &fastConfig)

	// make prediction
	var out float64
	var outLen64 int64
	cErr = gl.BoosterPredictForMatSingleRowFast(fastConfig, data, &outLen64, &out)
	fmt.Println("Predicted quality:", out)

	// batch
	//data1 := []float32{7.4, 0.7, 0, 1.9, 0.076, 11, 34, 0.9978, 3.51, 0.56, 10.1, 2.4, 0.7, 0, 1.9, 0.076, 11, 34, 0.9978, 3.51, 0.56, 10.1}
	var dataR []float32
	//maxValue := float32(10.0) // 设定最大值
	for i := 0; i < 100; i++ {
		for j := 0; j < 11; j++ {
			//randomNum := rand.Float32() * maxValue // 生成 [0, maxValue) 之间的随机数
			//dataR = append(dataR, randomNum)
			dataR = append(dataR, data...)
		}
	}

	nRaw, nCol := int32(100), int32(11)
	// 注意这个要提前分配内存
	outval := make([]float64, nRaw)

	gl.BoosterPredictForMat(booster, dataR, gl.Dtype_float32, nRaw, nCol, gl.Row_major, gl.PredictNormal,
		startIter, numIter, "", &outLen64, outval)
	fmt.Printf("outlen %d\n", outLen64)
	for i, v := range outval {
		fmt.Printf("outval %d, data %f\n", i, v)
	}
	// release
	cErr = gl.BoosterFree(booster)

}
