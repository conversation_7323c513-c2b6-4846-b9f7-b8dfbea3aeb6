# 代码改动说明

## 问题描述
程序运行时出现数组越界错误：
```
panic: runtime error: index out of range [34] with length 34
```

## 问题分析
1. `catfieldsNoPrice` 数组有35个元素（索引0-34）
2. 从模型文件解析的 `category` 数组只有34个元素（索引0-33）
3. 当循环到第35个元素（索引34）时，访问 `category[34]` 导致越界

## 解决方案

### 1. 修复数组越界问题
在 `cmd/bidding_mergex/adxbiddingV2.go` 第163-179行，添加了边界检查：

```go
categoryMap = make(catTable)
fmt.Printf("catfieldsNoPrice length: %d, category length: %d\n", len(catfieldsNoPrice), len(category))

for i, v := range catfieldsNoPrice {
    if v == "price" {
        continue
    }
    
    // 添加边界检查，防止数组越界
    if i >= len(category) {
        fmt.Printf("Warning: feature index %d (%s) exceeds category length %d, skipping\n", i, v, len(category))
        continue
    }
    
    categoryMap[v] = make(map[string]float64)
    for j, val := range category[i] {
        switch val := val.(type) {
        case string:
            categoryMap[v][val] = float64(j)
        case int, int32, int64, float32, float64:
            categoryMap[v][fmt.Sprintf("%v", val)] = float64(j)
        default:
            // 其他类型处理（如果需要）
        }
    }
}
```

### 2. 添加特征向量输出功能
在 `cmd/bidding_mergex/adxbiddingV2.go` 第224-255行，添加了特征向量打印功能：

```go
// 打印每行数据的所有特征向量，用制表符分隔
fmt.Println("=== 特征向量输出 ===")
for i, v := range feas {
    fmt.Printf("Row %d (ID: %s):\t", i, ids[i])
    for j, feature := range v {
        if j > 0 {
            fmt.Print("\t")
        }
        fmt.Printf("%.6f", feature)
    }
    fmt.Println()
}
fmt.Println("=== 特征向量输出结束 ===")
```

## 改动文件
- `cmd/bidding_mergex/adxbiddingV2.go`

## 改动效果
1. **修复崩溃问题**：程序不再因为数组越界而崩溃
2. **增加调试信息**：显示特征数组长度对比，帮助诊断问题
3. **输出特征向量**：按要求输出每行数据的所有特征向量，用制表符分隔
4. **跳过无效特征**：当特征索引超出范围时，会跳过并给出警告

## 注意事项
- 特征数量不匹配可能表明模型训练时使用的特征与当前代码定义的特征不一致
- 建议检查模型训练时使用的特征列表，确保与代码中的特征定义保持一致
